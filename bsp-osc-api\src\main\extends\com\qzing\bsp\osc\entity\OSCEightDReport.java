package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.ieep.data.common.DataPermission;
import com.qzing.ieep.datadriven.DataDrivenEntityListener;
import com.qzing.ieep.logging.listener.AuditListener;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

@Entity
@Table(name = "d_osc_eightdreport")
@NoArgsConstructor
@Getter
@Setter
@EntityListeners({AuditListener.class, DataDrivenEntityListener.class})
@DataPermission(billTypeCode = OSCBillType.EightDReport)
public class OSCEightDReport extends StdOSCEightDReport {

}

package com.qzing.bsp.osc.entity;

/**
 * 风险研判单 - 单据状态
 * <AUTHOR>
 * @date 2025/7/28
 */
public enum RiskJudgeState {
	/** 0 新建 */
	NEW("state.new"),
	/** 1 待审核 */
	CONFIRM("state.confirm"),
	/** 2 驳回（审批不过） */
	REJECT("state.reject"),
	/** 3 待研判 */
	TOJUDGE("state.tojudge"),
	/** 4 取消 */
	CANCEL("state.cancel"),
	/** 5 研判完成 */
	JUDGECOMPLETE("state.judgecomplete"),

	/** 6 未标注 */
	UNMARKED("state.unmarked"),
	/** 7 已标注 */
	MARKED("state.marked"),
	/** 8 关闭 */
	CLOSED("state.closed"),
	/** 9 空 */
	BLANK("state.blank"),
	/** 10 待派单 */
	PENDING_ASSIGN("state.pendingAssign"),
	/** 11 待执行 */
	PENDING_EXECUTE("state.pendingExecute"),
	/** 12 已完成 */
	FINISHED("state.finished");

	private final String desc;

	RiskJudgeState(String desc) {
		this.desc = desc;
	}

	public String desc() {
		return desc;
	}

	public Integer value() {
		return this.ordinal();
	}

	public String toString() {
		return String.valueOf(this.ordinal());
	}

	public int getIndex() {
		return this.ordinal();
	}

	public String getName() {
		return this.name();
	}

	public String getStateDesc() {
		return this.desc();
	}
}

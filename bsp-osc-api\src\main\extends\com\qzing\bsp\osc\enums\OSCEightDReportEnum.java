package com.qzing.bsp.osc.enums;

public enum OSCEightDReportEnum {
    /**
     * 问题提出
     */
    D0ProblemPropose,

    /**
     * 小组成立
     */
    D1GroupSetup,

    /**
     * 问题界定
     */
    D2ProblemDefine,

    /**
     * 围堵措施
     */
    D3ContainmentMeasure,

    /**
     * 原因分析
     */
    D4ReasonAnalysis,

    /**
     * 纠正措施
     */
    D5CorrectMeasure,

    /**
     * 验证效果
     */
    D6EffectVerify,

    /**
     * 预防再发生
     */
    D7RreventMeasures,

    /**
     * 结案评价
     *
     */
    D8ClosedEvaluate,

    /**
     * 已结案
     */
    Finish;
}

package com.qzing.bsp.osc.constant;

/**
 * 常量
 */
public abstract class OSCConstants {

    public static final String ACTION_SAVE = "save";
    public static final String ACTION_UPDATE = "update";
    public static final String ACTION_SUBMIT = "submit";
    public static final String ACTION_DELETE = "delete";
    public static final String ACTION_TOCONFIRM = "toConfirm";
    public static final String ACTION_TOPASS = "toPass";
    public static final String ACTION_TONOPASS = "toNoPass";
    public static final String ACTION_ACCEPT = "accept";
    public static final String ACTION_REFUSE = "refuse";
    public static final String ACTION_CLOSE = "close";
    public static final String ACTION_CANCEL = "cancel";
    public static final String ACTION_COPY = "copy";
    public static final String ACTION_PUBLISH = "publish";
    public static final String ACTION_UNCONFIRM = "unconfirm";
    public static final String ACTION_UNSING = "unsing";
    public static final String ACTION_BARGAINING = "bargaining";
    public static final String ACTION_TOOPEN = "execute";
    public static final String ACTION_TOCOMPLETE = "done";
    public static final String ACTION_TOSCORE = "toScore";
    public static final String ACTION_NULLIFY = "nullify";
    public static final String ACTION_TOCORRCOMP = "toCorrComp";
    public static final String ACTION_TOCORRING = "toCorrIng";
    public static final String ACTION_TOAFFIVALI = "toAffiVali";
    public static final String ACTION_TOAFFIINVALI = "toAffiInVali";
    public static final String ACTION_SYNC = "sync";
    public static final String ACTION_FREEZE = "freeze";
    public static final String ACTION_ENABLE = "enable";
    public static final String ACTION_DISABLED = "disable";
    public static final String ACTION_DELIED = "deliEd";
    public static final String ACTION_REVOKE = "revoke";
    public static final String ACTION_CONFIG = "config";
    public static final String ACTION_CONFIGSAVE = "configSave";
    public static final String ACTION_CHANGE = "change";
    public static final String ACTION_TOEND = "toend";
    public static final String ACTION_START = "start";
    public static final String ACTION_DELAY = "delay";
    public static final String ACTION_TOCONFIRMEXPIRE = "toconfirmexpire";
    public static final String ACTION_SCOREEDIT = "scoreEdit";
    public static final String ACTION_ABOLISH = "abolish";
    public static final String ACTION_CHECKING = "checking";
    public static final String ACTION_WRITEOFF = "writeOff";
    public static final String ACTION_IMPORT = "import";
    public static final String ACTION_COLLECTPOINTS = "collectpoints";
    public static final String ACTION_SCOREAUDIT = "scoreAudit";
    public static final String ACTION_OPEN = "open";
    public static final String ACTION_SHUT = "shut";
    public static final String ACTION_DELAYENDTIME = "delayendtime";
    public static final String ACTION_AUTOSTOP = "autoStop";
    public static final String ACTION_TRACK = "track";
    public static final String ACTION_SEARCH = "search";
    public static final String ACTION_PROGRESSSEARCH = "progressSearch";
    public static final String ACTION_UPPERSHEFF = "uppersheff";
    public static final String ACTION_LOWERSHEFF = "lowersheff";
    public static final String ACTION_CANCELSCALING = "cancelScaling";
    public static final String ACTION_CANCELAUCTION = "cancelAuction";
    public static final String ACTION_PROCESSINSERTUSER = "processInsertUser";
    public static final String ACTION_PROCESSSIGNROLE = "processSignRole";
    public static final String ACTION_PRESSINGFORAPPROVAL = "pressingforapproval";
    public static final String ACTION_CONFIRMHOLD = "confirmHold";
    public static final String ACTION_CONFIRMREJECT = "confirmReject";
    public static final String ACTION_CONFIRM = "confirm";
    public static final String ACTION_STOP = "stop";
    public static final String ACTION_SIGING = "signing";
    public static final String ACTION_MATURITY = "maturity";
    public static final String ACTION_END = "end";
    public static final String ACTION_RESENDMAIL = "resendMail";
    public static final String ACTION_REJECTTOB = "rejectToB";
    public static final String ACTION_REJECTTOV = "rejectToV";
    public static final String ACTION_BATCHIMPORT = "batchImport";
    public static final String ACTION_AUTOCREATE = "autoCreate";
    public static final String ACTION_EFFECTIVE = "effective";

    public static final String HEADER_REQUESTID = "requestId";
    public static final String HEADER_APPID = "Consumer";
    public static final String HEADER_VERSION = "version";
    public static final String HEADER_WORKFLOWENABLE = "workflowEnable";
    public static final String HEADER_ACTION = "action";
    public static final String HEADER_VENDORCODE = "Vendor-Code";
    public static final String HEADER_CONSUMER = "Consumer";
    public static final String HEADER_VENDORUSERNAME = "Vendor-User-Name";
    public static final String APPID_VENDOR = "QSC";

    //任务完成-主单
    public static final String NOTIFY_TASK_FINISH = "TASK_FINISH";
    //任务完成-执行明细
    public static final String NOTIFY_TASK_FINISH_ITEM = "TASK_FINISH_ITEM";
    //任务催办-负责人
    public static final String NOTIFY_TASK_URGING_OWNER = "TASK_URGING_OWNER";
    //任务催办-执行人
    public static final String NOTIFY_TASK_URGING_EXECUTOR = "TASK_URGING_EXECUTOR";

    //是
    public static final Integer BIND = 1;
    //否
    public static final Integer UNBIND = 0;

    public OSCConstants() {
    }

}

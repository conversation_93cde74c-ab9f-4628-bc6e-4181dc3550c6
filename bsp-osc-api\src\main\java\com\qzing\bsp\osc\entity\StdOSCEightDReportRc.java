package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Calendar;
import java.util.Date;

@MappedSuperclass
@NoArgsConstructor
@Getter
@Setter
public class StdOSCEightDReportRc extends OSCEntity<Long>  {

    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "reportRcId")
    @FieldMetadata(name = "ID", resourceKey = "label.reportId", skipHistoryComparison = true)
    protected Long reportRcId;

    @FieldMetadata(name = "是否接受问题", resourceKey = "QM.label.isAcceptProblem")
    protected Boolean isAcceptProblem;

    @FieldMetadata(name = "接受拒绝理由", resourceKey = "QM.label.acceptRefuseReason")
    protected String acceptRefuseReason;

    @FieldMetadata(name = "问题界定", resourceKey = "label.quality.definitionIssues")
    protected String problemDefine;

    @FieldMetadata(name = "D2 附件(8D)", resourceKey = "QM.label.d2FileGroupGid")
    protected Long d2FileGroupGid;

    @FieldMetadata(name = "围堵措施", resourceKey = "label.quality.containmentAction")
    protected String containmentMeasure;

    @FieldMetadata(name = "D3 直接结案", resourceKey = "label.quality.caseDirectly")
    protected Boolean d3DirectClosure;

    @FieldMetadata(name = "D3 直接结案理由", resourceKey = "QM.label.d3DirectClosureReason")
    protected String d3DirectClosureReason;

    @FieldMetadata(name = "原因分析", resourceKey = "label.quality.reasonAnalysis")
    protected String reasonAnalysis;

    @FieldMetadata(name = "D4附件(8D)", resourceKey = "QM.label.d4FileGroupGid")
    protected Long d4FileGroupGid;

    @FieldMetadata(name = "纠正措施", resourceKey = "label.quality.remedialAction")
    protected String correctMeasure;

    @Temporal(TemporalType.TIMESTAMP)
    @FieldMetadata(name = "纠正措施时间", resourceKey = "label.quality.positiveActionResponseTime")
    protected Calendar correctMeasureDate;

    @FieldMetadata(name = "执行人", resourceKey = "label.quality.executor")
    protected String executor;

    @Temporal(TemporalType.TIMESTAMP)
    @FieldMetadata(name = "执行日期", resourceKey = "label.quality.executionTime")
    protected Calendar executeTime;

    @FieldMetadata(name = "D5附件(8D)", resourceKey = "QM.label.d5FileGroupGid")
    protected Long d5FileGroupGid;

    @FieldMetadata(name = "效果验证", resourceKey = "label.quality.effectVerification")
    protected String effectVerify;

    @FieldMetadata(name = "是否有效", resourceKey = "label.quality.whetherStrategyEffective")
    protected Boolean isEffective;

    @FieldMetadata(name = "验证人", resourceKey = "label.quality.identifier")
    protected String verifier;

    @Temporal(TemporalType.TIMESTAMP)
    @FieldMetadata(name = "验证时间", resourceKey = "label.quality.proofTime")
    protected Calendar verifyTime;

    @FieldMetadata(name = "D6附件(8D)", resourceKey = "QM.label.d6FileGroupGid")
    protected Long d6FileGroupGid;

    @FieldMetadata(name = "预防措施", resourceKey = "label.quality.precautionaryMeasures")
    protected String preventMeasures;

    @FieldMetadata(name = "D7附件(8D)", resourceKey = "QM.label.d7FileGroupGid")
    protected Long d7FileGroupGid;

    @FieldMetadata(name = "D8附件(8D)", resourceKey = "QM.label.d8FileGroupGid")
    protected Long d8FileGroupGid;

    @FieldMetadata(name = "是否拒绝结案", resourceKey = "QM.label.isRefuseClosed")
    protected Boolean isRefuseClosed;

    @FieldMetadata(name = "D8拒绝结案理由", resourceKey = "label.quality.refuseCloseCase")
    protected String refuseReason;

    @FieldMetadata(name = "结案评价", resourceKey = "label.quality.finalEvaluation")
    protected String closedEvaluate;

}

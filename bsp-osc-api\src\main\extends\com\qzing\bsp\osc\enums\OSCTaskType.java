package com.qzing.bsp.osc.enums;

/**
 * 任务类型
 */
public enum OSCTaskType {

    Reception("交流接待"),
    SystemIssues("信息系统问题反馈"),
    HandleDoubts("疑点处置"),
    DataSend("资料发送"),
    OfflineDoubts("线下疑点发现");

    private String desc;

    private OSCTaskType(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }

}

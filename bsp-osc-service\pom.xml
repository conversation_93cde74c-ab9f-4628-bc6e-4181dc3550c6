<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qzing.bsp</groupId>
        <artifactId>bsp-osc-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <artifactId>bsp-osc-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-coding-template</artifactId>
        </dependency>
        <!--		<dependency>
                    <groupId>com.oscar</groupId>
                    <artifactId>oscarJDBC16</artifactId>
                </dependency>-->
        <dependency>
            <groupId>com.qzing.bsp</groupId>
            <artifactId>bsp-osc-api</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.qzing.ui</groupId>
            <artifactId>designer-runtime-all</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>velocity</artifactId>
                    <groupId>org.apache.velocity</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- ieep2-jar -->
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-admin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-iam-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-apimgmt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-notify-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-scheduler-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-logging-api</artifactId>
        </dependency>
        <!-- ieep2-starter -->
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-cache-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-cache-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-excel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mail</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-groovy</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-i18n</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-log</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-license</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-event-driven</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-mq-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-graphql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-export</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-starter-data-driven</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-dm-designer-api</artifactId>
        </dependency>
        <!-- MD
        <dependency>
            <groupId>com.qzing.md</groupId>
            <artifactId>md-org-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.qzing.md</groupId>
            <artifactId>md-mdm-api</artifactId>
        </dependency>
        -->
        <!-- qzing api
        <dependency>
            <groupId>com.qzing.bsp</groupId>
            <artifactId>srm-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qzing.service</groupId>
            <artifactId>service-base</artifactId>
        </dependency>
        -->
        <dependency>
            <groupId>e-iceblue</groupId>
            <artifactId>spire.pdf.free</artifactId>
            <version>3.9.0</version>
        </dependency>
        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis</artifactId>
            <version>1.4.01</version>
        </dependency>
        <dependency>
            <groupId>nu.pattern</groupId>
            <artifactId>opencv</artifactId>
            <version>2.4.9-4</version>
        </dependency>
        <!-- 排除 springboot 自带的 tomcat 依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 添加 tongweb-spring-boot-starter 依赖 -->
        <dependency>
            <groupId>com.tongweb.springboot</groupId>
            <artifactId>tongweb-spring-boot-starter-2.x</artifactId>
        </dependency>
        <!-- 添加嵌入式版 TongWeb 依赖 -->
        <dependency>
            <groupId>com.tongweb</groupId>
            <artifactId>tongweb-embed</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tongweb</groupId>
            <artifactId>tongweb-jsp</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <classifier>exec</classifier>
                    <mainClass>com.qzing.bsp.osc.OSCFullDeploy</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.entity.SceneUserRelation;
import com.qzing.ieep.coding.template.service.SimpleServiceTemplate;

import java.util.List;
import java.util.Map;

/**
 * 场景人员关系服务接口
 */
public interface StdSceneUserRelationService extends SimpleServiceTemplate<SceneUserRelation, Long> {
    void enable(List<Long> ids, Map<String, Object> extraInfo);

    void disable(List<Long> ids, Map<String, Object> extraInfo);

    void deleteData(List<Long> ids);
}

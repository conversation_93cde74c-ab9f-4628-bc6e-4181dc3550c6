package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.bsp.osc.enums.OSCTaskType;
import com.qzing.ieep.data.common.FieldMetadata;
import com.qzing.ieep.data.util.vo.FileInfoResponse;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.Calendar;
import java.util.List;

/**
 * 任务管理执行明细-基类
 */
@Setter
@Getter
@MappedSuperclass
public class StdOSCTaskDtl extends OSCEntity<Long> {

	private static final long serialVersionUID = 2526335682158437861L;

	@Id
	@GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
	@GeneratedValue(generator = "SnowFlakeIdGenerator")
	@Column(name = "taskDtlId")
	@FieldMetadata(name = "任务执行明细ID", resourceKey = "label.taskDtlId", skipHistoryComparison = true)
	protected Long taskDtlId;

	@ManyToOne
	@JoinColumn(name = "taskId", referencedColumnName = "taskId")
	protected OSCTask task;

	@Column(name = "rowNo")
	@FieldMetadata(name = "行号", resourceKey = "label.rowNo")
	protected Integer rowNo;

	@Column(name = "sourceNo")
	@FieldMetadata(name = "来源单号-风险研判单号", resourceKey = "label.riskAnalysisNo")
	protected String sourceNo;

	@Column(name = "sourceRowNo")
	@FieldMetadata(name = "来源单行号-风险研判细单号", resourceKey = "label.lineNo")
	protected String sourceRowNo;

	@Column(name = "sourceId")
	@FieldMetadata(name = "来源ID-风险研判细单id", resourceKey = "label.sourceId")
	protected Long sourceId;

	@Column(name = "sourceDesc")
	@FieldMetadata(name = "来源描述-风险疑点描述", resourceKey = "label.sourceDesc")
	protected String sourceDesc;

	@Column(name = "riskLevel")
	@FieldMetadata(name = "风险等级", resourceKey = "label.riskLevel")
	protected String riskLevel;

	@Column(name = "riskAnalysisDesc")
	@FieldMetadata(name = "风险研判说明", resourceKey = "label.riskAnalysisDesc")
	protected String riskAnalysisDesc;

	@FieldMetadata(name = "明细数据业务主键", skipHistoryComparison = true)
	protected Long businessPrimaryKey;

	@FieldMetadata(name = "明细数据唯一主键", skipHistoryComparison = true)
	protected Long uqId;

	@FieldMetadata(name = "结果模型id", skipHistoryComparison = true)
	protected Long resultModelId;

}

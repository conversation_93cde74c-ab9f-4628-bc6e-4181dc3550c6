package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.bsp.osc.entity.RiskSuspicionRecordDtl;
import com.qzing.bsp.osc.model.RiskSuspicionRecordAssignRequest;
import com.qzing.ieep.coding.template.service.SimpleServiceTemplate;
import com.qzing.ieep.data.common.RestResponse;

import java.util.List;
import java.util.Map;

/**
 * 风险疑点记录服务接口
 */
public interface StdRiskSuspicionRecordService extends SimpleServiceTemplate<RiskSuspicionRecord, Long> {


    void assign(RiskSuspicionRecordAssignRequest entity);


    void writeBackByRJS(Map<String, List<Long>> oldDtlMap, Map<String, List<Long>> newDtlMap, Integer isBind);

    RiskSuspicionRecord getBill(String billNo);

    RestResponse importDetailData(List<RiskSuspicionRecordDtl> requests);
}

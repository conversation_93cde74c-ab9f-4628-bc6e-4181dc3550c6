package com.qzing.bsp.osc.controller;

import com.qzing.ieep.data.common.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "风险疑点记录中间层接口")
@RestController
@RequestMapping("/oscrisksuspicionrecord")
public class RiskSuspicionRecordController extends StdRiskSuspicionRecordController {

}

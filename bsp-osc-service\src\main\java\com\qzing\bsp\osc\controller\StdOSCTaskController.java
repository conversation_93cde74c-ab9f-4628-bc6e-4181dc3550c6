package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.entity.OSCTaskDtl;
import com.qzing.bsp.osc.entity.OSCTaskProcessRecord;
import com.qzing.bsp.osc.service.OSCTaskService;
import com.qzing.ieep.coding.template.controller.ControllerTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.logging.properties.AddOperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(tags = "任务管理中间层接口")
//@Certificate(value = { "OSC" }, requiredType = RequiredType.ONE)
@AddOperateLog(ignore = true)
public class StdOSCTaskController extends ControllerTemplate<OSCTask, Long> {

    @Autowired
    protected OSCTaskService service;

    @ApiOperation(value = "详情")
    @PostMapping("/get")
    public RestResponse get(@RequestBody OSCTask request) {
        OSCTask model = service.get(request);
        return RestResponse.success(model);
    }

    @ApiOperation(value = "明细不分页")
    @PostMapping("/getdetail")
    public RestResponse getDetail(@RequestBody OSCTask request) {
        List<OSCTaskDtl> list = service.getDetail(request);
        return RestResponse.success(list);
    }

    @ApiOperation(value = "执行记录列表")
    @PostMapping("/findprocessrecordlist")
    public RestResponse findProcessRecordList(@RequestBody JpaSearchRequest request) {
        List<OSCTaskProcessRecord> recordList = service.findProcessRecordList(request);
        return RestResponse.success(recordList);
    }

    @ApiOperation(value = "接受")
    @PostMapping("/accept")
    public RestResponse accept(@RequestBody OSCTask request) {
        return service.accept(request);
    }

    @ApiOperation(value = "拒绝")
    @PostMapping("/refuse")
    public RestResponse refuse(@RequestBody OSCTask request) {
        return service.refuse(request);
    }



    @ApiOperation(value = "派单")
    @PostMapping("/redispatchdtl")
    public RestResponse reDispatchDtl(@RequestBody OSCTaskDtl request) {
        return service.reDispatchDtl(request);
    }

    @ApiOperation(value = "任务整单完成")
    @PostMapping("/finish")
    public RestResponse finish(@RequestBody OSCTask request) {
        return service.finish(request);
    }

    @ApiOperation(value = "任务执行完成")
    @PostMapping("/finishdtl")
    public RestResponse finishDtl(@RequestBody OSCTaskDtl request) {
        return service.finishDtl(request);
    }

    @ApiOperation(value = "关闭")
    @PostMapping("/close")
    public RestResponse close(@RequestBody OSCTaskDtl request) {
        return service.close(request);
    }

    @ApiOperation(value = "激活")
    @PostMapping("/activation")
    public RestResponse activation(@RequestBody OSCTaskDtl request) {
        return service.activation(request);
    }

    @ApiOperation(value = "催办任务负责人")
    @PostMapping("/urging")
    public RestResponse urging(@RequestBody OSCTask request) {
        return service.urging(request);
    }

    @ApiOperation(value = "催办任务执行人")
    @PostMapping("/urgingdtl")
    public RestResponse urgingDtl(@RequestBody OSCTaskDtl request) {
        return service.urgingDtl(request);
    }

    @ApiOperation(value = "任务预警")
    @PostMapping("/warning")
    public RestResponse warning(@RequestBody OSCTaskDtl request) {
        return service.warning(request);
    }

    @ApiOperation(value = "挂起")
    @PostMapping("/pending")
    public RestResponse pending(@RequestBody OSCTaskDtl request) {
        return service.pending(request);
    }

}

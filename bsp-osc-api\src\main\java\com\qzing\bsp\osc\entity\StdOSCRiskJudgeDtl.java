package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description [风险研判-明细]
 * @date 2025/7/25
*/
@Data
@MappedSuperclass
public class StdOSCRiskJudgeDtl extends OSCEntity<Long> {

    private static final long serialVersionUID = 3414522766721489874L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskJudgeDtlId")
    @FieldMetadata(name = "主键ID", resourceKey = "label.taskId", skipHistoryComparison = true)
    protected Long riskJudgeDtlId;

    @FieldMetadata(name = "主单", skipHistoryComparison = true)
    @ManyToOne
    @JoinColumn(name = "riskJudgeId", referencedColumnName = "riskJudgeId")
    protected OSCRiskJudge riskJudge;

    @Column(name = "sceneCode")
    @FieldMetadata(name = "场景编码", resourceKey = "OSCRISK.label.sceneCode")
    protected String sceneCode;

    @Column(name = "sceneName")
    @FieldMetadata(name = "场景名称", resourceKey = "OSCRISK.label.sceneName")
    protected String sceneName;

    @Column(name = "rowNo")
    @FieldMetadata(name = "行号", resourceKey = "label.rowNo")
    protected Integer rowNo;

    @Column(name = "sourceId")
    @FieldMetadata(name = "来源ID", resourceKey = "label.rowNo")
    protected Long sourceId;

    @Column(name = "sourceBillNo")
    @FieldMetadata(name = "风险疑点记录", resourceKey = "OSC.menu.RSRCatalog")
    protected String sourceBillNo;

    @Column(name = "tagStatus")
    @FieldMetadata(name = "标注状态, 未标注、已标注、关闭", resourceKey = "OSCRISK.label.tagStatus")
    protected RiskJudgeState tagStatus;

    @Column(name = "taskStatus")
    @FieldMetadata(name = "任务执行状态, 空、待派单、待执行、已完成、关闭", resourceKey = "OSCRISK.label.taskStatus")
    protected RiskJudgeState taskStatus;

    @Column(name = "riskLevel")
    @FieldMetadata(name = "风险等级", resourceKey = "OSCRISK.label.riskLevel")
    protected String riskLevel;

    @Column(name = "judgeDesc")
    @FieldMetadata(name = "研判说明", resourceKey = "OSCRISK.label.desc")
    protected String judgeDesc;

    @Column(name = "closeReason")
    @FieldMetadata(name = "关闭原因", resourceKey = "OSCRISK.label.closeReason")
    protected String closeReason;

    @Column(name = "companyCode")
    @FieldMetadata(name = "单位编码", resourceKey = "deliveryDtl.unitCode")
    protected String companyCode;

    @Column(name = "companyName")
    @FieldMetadata(name = "单位名称", resourceKey = "deliveryDtl.unitName")
    protected String companyName;

    @Column(name = "contractCode")
    @FieldMetadata(name = "合同编码", resourceKey = "label.contractCode")
    protected String contractCode;

    @Column(name = "contractName")
    @FieldMetadata(name = "合同名称", resourceKey = "label.contractName")
    protected String contractName;

    @Column(name = "contractTypeName")
    @FieldMetadata(name = "合同类型", resourceKey = "label.contractTypeName")
    protected String contractTypeName;

    @Column(name = "contractAmount")
    @FieldMetadata(name = "合同金额", resourceKey = "contract.totalAmount")
    protected BigDecimal contractAmount;

    @Column(name = "isRisk")
    @FieldMetadata(name = "是否风险", resourceKey = "OSCRISK.label.isRisk")
    protected Boolean isRisk;

    @Column(name = "businessDomain")
    @FieldMetadata(name = "业务域", resourceKey = "ABP.menu.BusinessDomain")
    protected String businessDomain;

    @Column(name = "taskNo")
    @FieldMetadata(name = "任务单", resourceKey = "OSC.menu.TASK")
    protected String taskNo;

    @Column(name = "taskResult")
    @FieldMetadata(name = "任务结果录入", resourceKey = "label.equiptmentModel.taskResult.input")
    protected String taskResult;

    @Column(name = "superviseItemCode")
    @FieldMetadata(name = "监管项", resourceKey = "OSCRISK.label.superviseItem")
    protected String superviseItemCode;

    @Column(name = "superviseItemName")
    @FieldMetadata(name = "监管项", resourceKey = "OSCRISK.label.superviseItem")
    protected String superviseItemName;

    @Column(name = "tag")
    @FieldMetadata(name = "标签", resourceKey = "label.label")
    protected String tag;

    @Column(name = "businessPrimaryKey", length = 1024)
    @FieldMetadata(name = "明细数据业务主键", resourceKey = "label.businessPrimaryKey")
    protected String businessPrimaryKey;

    @Column(name = "uqId")
    @FieldMetadata(name = "明细数据唯一主键", resourceKey = "label.uqId")
    protected String uqId;

    @Column(name = "resultModelId")
    @FieldMetadata(name = "结果模型ID", resourceKey = "label.resultModelId")
    protected Long resultModelId;

}

package com.qzing.bsp.osc.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.qzing.bpm.support.annotation.BpmService;
import com.qzing.bpm.util.BpmUtils;
import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.constant.OSCConstants;
import com.qzing.bsp.osc.dao.OSCRiskJudgeDtlDao;
import com.qzing.bsp.osc.entity.OSCRiskJudge;
import com.qzing.bsp.osc.entity.OSCRiskJudgeDtl;
import com.qzing.bsp.osc.entity.RiskJudgeState;
import com.qzing.bsp.osc.entity.RiskSuspicionRecordDtl;
import com.qzing.core.sys.entity.BillType;
import com.qzing.ieep.coding.template.service.ServiceImplTemplate;
import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.data.common.Page;
import com.qzing.ieep.data.jpa.entity.BaseEntity;
import com.qzing.ieep.data.jpa.entity.BizEntity;
import com.qzing.ieep.data.jpa.entity.IFileInfo;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.data.validation.ValidateMessage;
import com.qzing.ieep.event.biz.SubmitEvent;
import com.qzing.ieep.event.biz.UpdateAsyncEvent;
import com.qzing.ieep.event.biz.UpdateEvent;
import com.qzing.ieep.file.api.util.FileInfoUtils;
import com.qzing.ieep.iam.entity.User;
import com.qzing.ieep.util.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEvent;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 风险评判
 */
@BpmService(billTypeCode = OSCBillType.RJS, billNoKey = OSCBillType.RISKJUDGENO)
@ModuleInfo(moduleCode = OSCBillType.RJS)
public class StdOSCRiskJudgeServiceImpl extends ServiceImplTemplate<OSCRiskJudge, Long> implements StdOSCRiskJudgeService {

    @Autowired
    protected OSCRiskJudgeDtlDao riskJudgeDtlDao;
    @Autowired
    protected RiskSuspicionRecordService riskSuspicionRecordService;

    /**
     * 获取详情
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    public OSCRiskJudge get(String riskJudgeNo, Map<String, Object> extraInfo) {
        OSCRiskJudge response = new OSCRiskJudge();
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("EQ_riskJudgeNo", riskJudgeNo);
        OSCRiskJudge entity = findOne(searchMap);
        if (null != entity) {
            BeanUtils.copyProperties(entity, response, "riskJudgeDtls");
        }
        return response;
    }

    /**
     * 明细
     * @param riskJudgeNo
     * @param extraInfo
     * @return
     */
    public List<OSCRiskJudgeDtl> getDetail(String riskJudgeNo, Map<String, Object> extraInfo) {
        List<OSCRiskJudgeDtl> oscRiskJudgeDtlResponseList = new ArrayList<>();
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("EQ_riskJudge_riskJudgeNo", riskJudgeNo);
        List<OSCRiskJudgeDtl> detailList = riskJudgeDtlDao.findAll(searchMap);
        if (null != detailList) {
            for (OSCRiskJudgeDtl judgeDtl: detailList) {
                OSCRiskJudgeDtl responseDtl = new OSCRiskJudgeDtl();
                BeanUtils.copyProperties(judgeDtl, responseDtl, "riskJudge");
                oscRiskJudgeDtlResponseList.add(responseDtl);
            }
        }
        return oscRiskJudgeDtlResponseList;
    }

    /**
     * 设置一对多
     * @param entity
     */
    @Override
    protected void setEntityRelation(OSCRiskJudge entity) {
        if (null != entity.getRiskJudgeDtls()) {
            for (OSCRiskJudgeDtl item: entity.getRiskJudgeDtls()) {
                item.setRiskJudge(entity);
            }
        }
    }

    @Override
    protected void beforeSave(OSCRiskJudge entity, Map<String, Object> extraMap) {
        String riskJudgeNo = billSetClient.createNextRunningNum(OSCBillType.RJS);
        entity.setRiskJudgeNo(riskJudgeNo);
        entity.setStatus(RiskJudgeState.NEW);
    }

    @Override
    protected void afterSave(OSCRiskJudge entity, Map<String, Object> extraMap) {
        callBackToRiskSuspicionRecord(null, entity.getRiskJudgeDtls(), OSCConstants.BIND);
    }

    @Override
    protected OSCRiskJudge afterBpmSubmit(OSCRiskJudge entity, Long userId, @Deprecated List<User> assignees, Map<String, Object> properties) {
        entity.setStatus(RiskJudgeState.CONFIRM);
        return entity;
    }

    @Override
    protected OSCRiskJudge afterBpmSubmitRevoke(OSCRiskJudge entity, Long userId, Long createUserId, List<Long> auditUserIds, String message, Map<String, Object> properties) {
        entity.setStatus(RiskJudgeState.NEW);
        return entity;
    }

    @Override
    protected OSCRiskJudge afterReject(OSCRiskJudge entity, Long userId, Long createUserId, String message, Map<String, Object> properties) {
        entity.setStatus(RiskJudgeState.REJECT);
        return entity;
    }

    @Override
    protected OSCRiskJudge afterComplete(OSCRiskJudge entity, Long userId, Long createUserId, String message, Map<String, Object> properties) {
        entity.setStatus(RiskJudgeState.TOJUDGE);
        return entity;
    }

    @Override
    protected void afterCancel(OSCRiskJudge entity, String message, Map<String, Object> extraMap) {
        entity.setStatus(RiskJudgeState.CANCEL);
        callBackToRiskSuspicionRecord(null, entity.getRiskJudgeDtls(), OSCConstants.UNBIND);
    }


    /**
     * 列表
     * @param request
     * @return
     */
    public Page<OSCRiskJudge> list(JpaSearchRequest request) {
        if (StringUtils.isBlank(request.getSorted())) {
            request.setSorted("riskJudgeId desc");
        }
        Class<OSCRiskJudge> voClass = getVoClass();

        if (voClass != null) {
            request.setResultType(voClass);
        }

        beforeList(request);

        Page<OSCRiskJudge> page = searchPage(request);

        List<OSCRiskJudge> responseList = new ArrayList<>();
        if (null != page.getRecords() && 0 != page.getRecords().size()) {
            for (OSCRiskJudge oscRiskJudge: page.getRecords()) {
                OSCRiskJudge response = new OSCRiskJudge();
                BeanUtils.copyProperties(oscRiskJudge, response, "riskJudgeDtls");
                responseList.add(response);
            }
            page.setRecords(responseList);
            BpmUtils.setAuditFlags(responseList, getBillType(), CurrentContextHelper.getUserId());

        }
        if (voClass != null && IFileInfo.class.isAssignableFrom(voClass)) {
            FileInfoUtils.buildFileInfo(page.getRecords());
        }
        return page;
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    @Override
    public Page<OSCRiskJudgeDtl> detailList(JpaSearchRequest request) {
        if (StrUtil.isBlank(request.getSorted())) {
            request.setSorted("riskJudgeDtlId desc,rowNo asc");
        }
        request.getParams().put("DISTINCT", false);
        Page<OSCRiskJudgeDtl> pageResult = riskJudgeDtlDao.searchPage(request);
        List<OSCRiskJudgeDtl> records = pageResult.getRecords();
        List<OSCRiskJudgeDtl> responseRecords = new ArrayList<>();

        if (CollUtil.isNotEmpty(records)) {
            for (OSCRiskJudgeDtl riskJudgeDtl: records) {
                OSCRiskJudgeDtl response = new OSCRiskJudgeDtl();
                BeanUtils.copyProperties(riskJudgeDtl, response, "riskJudge");
                OSCRiskJudge riskJudge = new OSCRiskJudge();
                BeanUtils.copyProperties(riskJudgeDtl.getRiskJudge(), riskJudge, "riskJudgeDtls");
                response.setRiskJudge(riskJudge);
                responseRecords.add(response);
            }
        }
        pageResult.setRecords(responseRecords);
        return pageResult;
    }


    /**
     * 添加日志
     * @param actionId
     * @param ei
     * @param billId
     */
    /*public void addLog(String actionId, ExtraInfo ei, Long billId){
        String requestId = ei.getRequestId();
        OperateLog operateLog = OperateLogUtils.OperateLogBuilder.builder()
                .action(actionId)
                .moduleCode(getModuleCode())
                .bizkey(billId.toString())
                .terminal(CurrentContextHelper.getTerminal())
                .operatorHost(CurrentContextHelper.getUserRealIp())
                .operatorId(CurrentContextHelper.getUserId())
                .operatorName(CurrentContextHelper.getUserName())
                .requestId(requestId)
                .requestTime(CurrentContextHelper.getRequestTime())
                .responseTime(Calendar.getInstance())
                .businessNo(billId.toString())
                .module(getModuleCode())
                .message(ei.getString(""))
                .content(ei.getString(""))
                .contentParamJson(ei.getReplace()).build();

        OperateLogUtils.log(operateLog);
    }*/

    @Override
    public OSCRiskJudge update(OSCRiskJudge entity, Map<String, Object> extraMap) {
        Long serializable = this.dao.getEntityId(entity);

        BizEntity bizEntity = (BizEntity)findById(serializable);

        extraMap.put("entity", bizEntity);

        setEntityRelation(entity);

        validate(entity, extraMap);

        validateDataChanged(entity, (OSCRiskJudge)bizEntity);

        beforeUpdate(entity, extraMap);

        OSCRiskJudge newEntity = doUpdate(entity, extraMap);
        if (null != newEntity) {

            String billType = getBillType(entity);

            afterUpdate(entity, extraMap);
            UpdateEvent updateEvent = new UpdateEvent(this, billType, (BaseEntity)newEntity, extraMap);
            this.applicationEventPublisher.publishEvent((ApplicationEvent)updateEvent);

            callBackToRiskSuspicionRecord(((OSCRiskJudge) bizEntity).getRiskJudgeDtls(), entity.getRiskJudgeDtls(), OSCConstants.BIND);
        }


        return entity;
    }

    @Override
    public OSCRiskJudge submit(OSCRiskJudge entity, Map<String, Object> extraMap) {
        Long serializable = this.dao.getEntityId(entity);

        BizEntity bizEntity = (BizEntity)findById(serializable);
        extraMap.put("entity", bizEntity);

        validate(entity, extraMap);

        validateDataChanged(entity, (OSCRiskJudge)bizEntity);

        setEntityRelation(entity);

        beforeSubmit(entity, extraMap);

        OSCRiskJudge newEntity = doSubmit(entity, extraMap);
        if (null != newEntity) {

            afterSubmit(entity, extraMap);

            String billType = getBillType(entity);
            SubmitEvent submitEvent = new SubmitEvent(this, billType, (BaseEntity)newEntity, extraMap);
            this.applicationEventPublisher.publishEvent((ApplicationEvent)submitEvent);
            callBackToRiskSuspicionRecord(((OSCRiskJudge) bizEntity).getRiskJudgeDtls(), entity.getRiskJudgeDtls(), OSCConstants.BIND);

        }
        return entity;
    }

    /**
     * @param oldList
     * @param newList
     * @param isBind
     * <AUTHOR>
     * @description [回写上游单据状态]
     * @date 2025/7/29
    */
    protected void callBackToRiskSuspicionRecord(List<OSCRiskJudgeDtl> oldList, List<OSCRiskJudgeDtl> newList, Integer isBind) {
        /*1. 旧数据解绑*/
        Map<String, List<Long>> oldMap = new HashMap<>();
        if (null != oldList) {
            for (OSCRiskJudgeDtl oldDtl : oldList) {
                String sourceBillNo = oldDtl.getSourceBillNo(); // 来源的主单号
                List<Long> sourceIdList = oldMap.get(sourceBillNo);
                if (null == sourceIdList) {
                    sourceIdList = new ArrayList<>();
                }
                Long sourceId = oldDtl.getSourceId(); //来源的明细id
                sourceIdList.add(sourceId);
                oldMap.put(sourceBillNo, sourceIdList);
            }
        }
        /*2. 新数据绑定*/
        Map<String, List<Long>> newMap = new HashMap<>();
        if (null != newList) {
            for (OSCRiskJudgeDtl newDtl : oldList) {
                String sourceBillNo = newDtl.getSourceBillNo(); // 来源的主单号
                List<Long> sourceIdList = newMap.get(sourceBillNo);
                if (null == sourceIdList) {
                    sourceIdList = new ArrayList<>();
                }
                Long sourceId = newDtl.getSourceId(); //来源的明细id
                sourceIdList.add(sourceId);
                oldMap.put(sourceBillNo, sourceIdList);
            }
        }

        riskSuspicionRecordService.writeBackByRJS(oldMap, newMap, isBind);
    }
}

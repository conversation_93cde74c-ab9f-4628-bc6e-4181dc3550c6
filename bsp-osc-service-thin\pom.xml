<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qzing.bsp</groupId>
        <artifactId>bsp-osc-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>bsp-osc-service-thin</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.qzing.bsp</groupId>
            <artifactId>bsp-osc-service</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>

        <plugins>
            <!-- jar抽取瘦身 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <configuration>
                    <createDependencyReducedPom>false</createDependencyReducedPom>
                </configuration>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <!-- qzing的jar包也打包进来 -->
                            <artifactSet>
                                <includes>
                                    <include>
                                        com.qzing*
                                    </include>
                                    <include>
                                        com.huiju*
                                    </include>
                                </includes>
                            </artifactSet>
                            <finalName>
                                bsp-osc-deploy-thin-${project.version}-exec</finalName>
                            <shadedArtifactAttached>true</shadedArtifactAttached>
                            <shadedClassifierName>exec</shadedClassifierName>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <!-- 第三方jar抽取 -->
                <configuration>
                    <archive>
                        <manifest>
                            <!-- 是否绑定依赖，将外部jar包依赖加入到classPath中 -->
                            <addClasspath>true</addClasspath>
                            <!-- 依赖前缀，公用的lib目录 -->
                            <classpathPrefix>lib/</classpathPrefix>
                            <!-- 主函数的入口 -->
                            <mainClass>com.qzing.bsp.osc.OSCFullDeploy</mainClass>
                            <useUniqueVersions>false</useUniqueVersions>
                        </manifest>
                        <manifestEntries>
                            <!--MANIFEST.MF 中 Class-Path 加入资源文件目录 -->
                            <Class-Path>resources/</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
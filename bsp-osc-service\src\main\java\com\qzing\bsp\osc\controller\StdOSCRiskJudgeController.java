package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.OSCRiskJudge;
import com.qzing.bsp.osc.entity.OSCRiskJudgeDtl;
import com.qzing.bsp.osc.service.OSCRiskJudgeService;
import com.qzing.ieep.coding.template.controller.ControllerTemplate;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.license.annotation.Certificate;
import com.qzing.ieep.license.annotation.Certificate.RequiredType;
import com.qzing.ieep.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(tags = "风险评判中间层接口")
//@Certificate(value = { "OSC" }, requiredType = RequiredType.ONE)
public class StdOSCRiskJudgeController extends ControllerTemplate<OSCRiskJudge, Long> {

    @Autowired
    protected OSCRiskJudgeService service;

    @ApiOperation(value = "详情")
    @PostMapping("/get")
    public RestResponse get(@RequestBody OSCRiskJudge request) {
        if (StringUtils.isBlank(request.getRiskJudgeNo())) {
            BizException.throwEx(String.format(getText("Common.message.fieldNotNull"), getText("OSCRISK.label.riskJudgeNo")));
        }
        OSCRiskJudge model = service.get(request.getRiskJudgeNo(), getUserInfo());
        return RestResponse.success(model);
    }

    @ApiOperation(value = "获取细单详情")
    @PostMapping("/getdetail")
    public RestResponse getDetail(@RequestBody OSCRiskJudge request) {
        if (StringUtils.isBlank(request.getRiskJudgeNo())) {
            BizException.throwEx(String.format(getText("Common.message.fieldNotNull"), getText("OSCRISK.label.riskJudgeNo")));
        }
        List<OSCRiskJudgeDtl> dtlList = service.getDetail(request.getRiskJudgeNo(), getUserInfo());
        return RestResponse.success(dtlList);
    }

}

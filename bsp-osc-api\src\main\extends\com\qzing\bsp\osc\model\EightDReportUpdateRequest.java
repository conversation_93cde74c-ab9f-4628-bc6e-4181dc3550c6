package com.qzing.bsp.osc.model;

import com.qzing.bsp.osc.annotation.OSCFieldRequired;
import com.qzing.ieep.data.util.vo.FileInfoResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Calendar;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@ApiModel("更新")
public class EightDReportUpdateRequest implements Serializable {
    private static final long serialVersionUID = 5180125584416159587L;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "reportId")
    protected Long reportId;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "改进对象")
    protected String  improveObject;

    @ApiModelProperty(value = "物料名称")
    protected String materialName;

    @ApiModelProperty(value = "物料编码")
    protected String materialCode;

    @ApiModelProperty(value = "规格型号")
    protected String materialDesc;

    @ApiModelProperty(value = "物料组编码")
    protected String materialGroupCode;

    @ApiModelProperty(value = "物料组名称")
    protected String materialGroupName;

    @ApiModelProperty(value = "批次号")
    protected String lotCode;

    @ApiModelProperty(value = "来源单id")
    protected Long sourceId;

    @ApiModelProperty(value = "来源单号")
    protected String sourceNo;

    @ApiModelProperty(value = "来源主单号")
    protected String sourceMainNo;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "缺陷等级")
    protected String defectGrade;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "重复发生次数")
    protected Integer repeatTimes;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "回复期限")
    protected Calendar replyPeriod;

    @OSCFieldRequired(area = {"problemPropose"})
    @ApiModelProperty(value = "问题描述")
    protected String problemDesc;

    @ApiModelProperty("D0 附件(8D)")
    protected Long d0FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d0FileInfoList;

    @ApiModelProperty(value = "备注")
    protected String remark;

    @ApiModelProperty(value = "采购组织编码")
    protected String purchasingOrgCode;

    @ApiModelProperty(value = "采购组织名称")
    protected String purchasingOrgName;

    @ApiModelProperty(value = "供应商问题来源")
    protected String problemOrigin;

    @ApiModelProperty(value = "供应商srm编码")
    protected String vendorCode;

    @ApiModelProperty(value = "供应商名称")
    protected String vendorName;

    @ApiModelProperty(value = "客户编码")
    protected String customerCode;

    @ApiModelProperty(value = "客户名称")
    protected String customerName;

    @ApiModelProperty(value = "内部改进问题来源")
    protected String internalProblemOrigin;

    @ApiModelProperty(value = "责任部门编码")
    protected String dutyDepartmentCode;

    @ApiModelProperty(value = "责任部门名称")
    protected String dutyDepartmentName;

    @ApiModelProperty(value = "责任部门负责人编码")
    protected String dutyDeptUserCode;

    @ApiModelProperty(value = "责任部门负责人")
    protected String dutyDeptUserName;

    @ApiModelProperty(value = "供应商erp编码")
    protected String vendorErpCode;

    @ApiModelProperty(value = "工厂编码")
    protected String plantCode;

    @ApiModelProperty(value = "工厂名称")
    protected String plantName;

}

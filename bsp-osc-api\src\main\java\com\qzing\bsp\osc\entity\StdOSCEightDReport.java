package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import java.util.List;

@MappedSuperclass
@NoArgsConstructor
@Getter
@Setter
public class StdOSCEightDReport extends OSCEntity<Long>  {

    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "reportId")
    @FieldMetadata(name = "ID", resourceKey = "label.reportId", skipHistoryComparison = true)
    protected Long reportId;

    @FieldMetadata(name = "单据状态", resourceKey = "label.quality.documentStatus")
    protected String billStatus;

    @FieldMetadata(name = "问题描述", resourceKey = "label.quality.problemDescription")
    protected String problemDesc;

    @FieldMetadata(name = "D0 附件(8D)", resourceKey = "label.quality.attachment")
    protected Long d0FileGroupGid;

    @Column(name = "departmentCode")
    @FieldMetadata(name = "负责部门名称", resourceKey = "label.unitName")
    protected String departmentCode;

    @Column(name = "departmentName")
    @FieldMetadata(name = "负责部门编码", resourceKey = "label.unitCode")
    protected String departmentName;

    @Column(name = "companyName")
    @FieldMetadata(name = "单位名称", resourceKey = "label.sourceUnitName")
    protected String companyName;

    @Column(name = "companyCode")
    @FieldMetadata(name = "单位编码", resourceKey = "label.sourceUnitCode")
    protected String companyCode;

    @Column(name = "taskOwnerCode")
    @FieldMetadata(name = "任务责任人编码", resourceKey = "label.taskOwner")
    protected String taskOwnerCode;

    @Column(name = "taskOwnerName")
    @FieldMetadata(name = "任务责任人名称", resourceKey = "label.taskOwner")
    protected String taskOwnerName;

    @Column(name = "ownerUserId")
    @FieldMetadata(name = "任务责任人用户ID", resourceKey = "label.ownerUserId")
    protected Long ownerUserId;

    @Column(name = "taskNo")
    @FieldMetadata(name = "任务单号", resourceKey = "label.taskNo")
    protected String taskNo;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "reportRcId", referencedColumnName = "reportRcId")
    @FieldMetadata(name = "改进处置信息", resourceKey = "label.quality.firstPieceInspection")
    protected OSCEightDReportRc eightDReportRc;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "taskId", referencedColumnName = "taskId")
    @FieldMetadata(name = "任务单", resourceKey = "OSC.menu.TASK")
    protected OSCTask task;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "eightDReport", fetch = FetchType.LAZY, orphanRemoval = true)
    @FieldMetadata(name = "小组成员明细", resourceKey = "QM.label.eightDReportDetailList")
    protected List<OSCEightDReportDetail> eightDReportDetailList;

}

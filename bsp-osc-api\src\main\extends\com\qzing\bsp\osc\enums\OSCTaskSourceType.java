package com.qzing.bsp.osc.enums;

/**
 * 任务来源类型
 */
public enum  OSCTaskSourceType {

    Daily("日常任务"),
    RiskAssess("风险研判单");

    private String desc;

    private OSCTaskSourceType(String desc) {
        this.desc = desc;
    }

    public String desc() {
        return this.desc;
    }

    public Integer value() {
        return this.ordinal();
    }

    public String toString() {
        return String.valueOf(this.ordinal());
    }

    public int getIndex() {
        return this.ordinal();
    }

    public String getName() {
        return this.name();
    }

    public String getStateDesc() {
        return this.desc();
    }
}

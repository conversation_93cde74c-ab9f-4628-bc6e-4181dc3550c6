package com.qzing.bsp.osc.entity;


import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 风险疑点记录主单实体类-基类
 */
@Data
@MappedSuperclass
public class StdRiskSuspicionRecord extends OSCEntity<Long> {

    private static final long serialVersionUID = 2526335682158437861L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskSuspicionRecordId")
    @FieldMetadata(name = "记录ID", resourceKey = "label.recordId", skipHistoryComparison = true)
    protected Long riskSuspicionRecordId;

    @BizNumber
    @Column(name = "riskSuspicionRecordNo")
    @FieldMetadata(name = "单号", resourceKey = "label.riskSuspicionRecordNo")
    protected String riskSuspicionRecordNo;

    @Column(name = "sceneCode")
    @FieldMetadata(name = "场景编码", resourceKey = "label.sceneCode")
    protected String sceneCode;

    @Column(name = "sceneName")
    @FieldMetadata(name = "场景名称", resourceKey = "label.sceneName")
    protected String sceneName;

    @Column(name = "status")
    @FieldMetadata(name = "状态", resourceKey = "label.status")
    protected OSCState status;

    @Column(name = "companyCode")
    @FieldMetadata(name = "单位编码", resourceKey = "label.companyCode")
    protected String companyCode;

    @Column(name = "companyName")
    @FieldMetadata(name = "单位名称", resourceKey = "label.companyName")
    protected String companyName;

    @Column(name = "businessDomainCode")
    @FieldMetadata(name = "业务域编码", resourceKey = "label.businessDomainCode")
    protected String businessDomainCode;

    @Column(name = "businessDomainName")
    @FieldMetadata(name = "业务域名称", resourceKey = "label.businessDomainName")
    protected String businessDomainName;

    @Column(name = "dtlCount")
    @FieldMetadata(name = "统计数(疑点明细数量)", resourceKey = "label.dtlCount")
    protected Integer dtlCount;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "runTime")
    @FieldMetadata(name = "运行时间", resourceKey = "label.runTime")
    protected Calendar runTime;

    @Column(name = "source")
    @FieldMetadata(name = "来源", resourceKey = "label.source")
    protected String source;

    @Column(name = "assignedPersonCode")
    @FieldMetadata(name = "责任人员编码", resourceKey = "label.assignedPersonCode")
    protected String assignedPersonCode;

    @Column(name = "assignedPersonName")
    @FieldMetadata(name = "责任人员姓名", resourceKey = "label.assignedPersonName")
    protected String assignedPersonName;

    @Column(name = "detailedMetadatas")
    @FieldMetadata(name = "明细元数据汇总", resourceKey = "COMMON.label.judgeContent")
    protected String detailedMetadatas;


    @OneToMany(cascade = CascadeType.ALL, mappedBy = "riskSuspicionRecord", fetch = FetchType.LAZY, orphanRemoval = true)
    @FieldMetadata(name = "疑点明细", resourceKey = "label.suspicionDetails")
    protected List<RiskSuspicionRecordDtl> suspicionDetails = new ArrayList<RiskSuspicionRecordDtl>();
}

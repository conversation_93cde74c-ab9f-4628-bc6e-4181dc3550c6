package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.bsp.osc.enums.OSCTaskSourceType;
import com.qzing.bsp.osc.enums.OSCTaskType;
import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import com.qzing.ieep.data.util.vo.FileInfoResponse;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.OneToMany;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 任务管理实体类-基类
 */
@Data
@MappedSuperclass
public class StdOSCTask extends OSCEntity<Long> {

	private static final long serialVersionUID = 2526335682158437861L;

	@Id
	@GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
	@GeneratedValue(generator = "SnowFlakeIdGenerator")
	@Column(name = "taskId")
	@FieldMetadata(name = "任务ID", resourceKey = "label.taskId", skipHistoryComparison = true)
	protected Long taskId;

	@BizNumber
	@Column(name = "taskNo")
	@FieldMetadata(name = "任务单号", resourceKey = "label.taskNo")
	protected String taskNo;

	@Column(name = "state")
	@FieldMetadata(name = "任务状态", resourceKey = "label.state")
	protected OSCState state;

	@Column(name = "sourceType")
	@FieldMetadata(name = "来源类型", resourceKey = "label.sourceType")
	protected OSCTaskSourceType sourceType;

	@Column(name = "taskType")
	@FieldMetadata(name = "任务类型", resourceKey = "label.taskType")
	protected OSCTaskType taskType;

	@Column(name = "taskOwnerCode")
	@FieldMetadata(name = "任务责任人编码", resourceKey = "label.taskOwner")
	protected String taskOwnerCode;

	@Column(name = "taskOwnerName")
	@FieldMetadata(name = "任务责任人名称", resourceKey = "label.taskOwner")
	protected String taskOwnerName;

	@Column(name = "ownerUserCode")
	@FieldMetadata(name = "任务责任人用户编码", resourceKey = "label.ownerUserCode")
	protected String ownerUserCode;

	@Column(name = "ownerUserId")
	@FieldMetadata(name = "任务责任人用户ID", resourceKey = "label.ownerUserId")
	protected Long ownerUserId;

	@Column(name = "departmentCode")
	@FieldMetadata(name = "负责部门名称", resourceKey = "label.unitName")
	protected String departmentCode;

	@Column(name = "departmentName")
	@FieldMetadata(name = "负责部门编码", resourceKey = "label.unitCode")
	protected String departmentName;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "requireFinishDate")
	@FieldMetadata(name = "要求完成日期", resourceKey = "label.requiredCompletionDate")
	protected Calendar requireFinishDate;

	@Column(name = "taskTitle")
	@FieldMetadata(name = "任务标题", resourceKey = "label.taskTitle")
	protected String taskTitle;

	@Column(name = "companyName")
	@FieldMetadata(name = "单位名称", resourceKey = "label.sourceUnitName")
	protected String companyName;

	@Column(name = "companyCode")
	@FieldMetadata(name = "单位编码", resourceKey = "label.sourceUnitCode")
	protected String companyCode;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "dispatchTime")
	@FieldMetadata(name = "派单时间", resourceKey = "label.dispatchTime")
	protected Calendar dispatchTime;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "actualFinishTime")
	@FieldMetadata(name = "实际完成时间", resourceKey = "label.actualCompletionTime")
	protected Calendar actualFinishTime;

	@Column(name = "completionDesc")
	@FieldMetadata(name = "完成说明", resourceKey = "label.completionDesc")
	protected String completionDesc;

	@Column(name = "finishFileGroupId")
	@FieldMetadata(name = "完成提交附件", resourceKey = "label.completionAttachment")
	protected Long finishFileGroupId;

	@Transient
	@FieldMetadata(name = "完成提交附件列表", skipHistoryComparison = true, resourceKey = "label.annex")
	protected List<FileInfoResponse> finishFileInfoList;

	@Column(name = "disposalSuggest")
	@FieldMetadata(name = "处置建议", resourceKey = "label.disposalSuggest")
	protected String disposalSuggest;

	@FieldMetadata(name = "任务执行明细", resourceKey = "label.taskDtls")
	@OneToMany(cascade = CascadeType.ALL, mappedBy = "task", fetch = FetchType.LAZY, orphanRemoval = true)
	protected List<OSCTaskDtl> taskDtls = new ArrayList<>();

	@Transient
	@FieldMetadata(name = "操作消息", skipHistoryComparison = true)
	protected String message;

}

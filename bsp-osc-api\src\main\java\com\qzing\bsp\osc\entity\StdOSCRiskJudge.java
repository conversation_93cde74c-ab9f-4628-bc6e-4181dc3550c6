package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description [风险研判]
 * @date 2025/7/25
*/
@Data
@MappedSuperclass
public class StdOSCRiskJudge extends OSCEntity<Long> {


    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskJudgeId")
    @FieldMetadata(name = "主键ID", resourceKey = "label.taskId", skipHistoryComparison = true)
    protected Long riskJudgeId;

    @BizNumber
    @Column(name = "riskJudgeNo")
    @FieldMetadata(name = "风险研判单号", resourceKey = "OSCRISK.label.riskJudgeNo")
    protected String riskJudgeNo;

    @Column(name = "status")
    @FieldMetadata(name = "单据状态", resourceKey = "label.states")
    protected RiskJudgeState status;

    @Column(name = "judgeContent")
    @FieldMetadata(name = "研判内容", resourceKey = "COMMON.label.judgeContent")
    protected String judgeContent;

    @Column(name = "detailedMetadatas")
    @FieldMetadata(name = "明细元数据汇总", resourceKey = "COMMON.label.judgeContent")
    protected String detailedMetadatas;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "riskJudge", fetch = FetchType.LAZY, orphanRemoval = true)
    @FieldMetadata(resourceKey = "OSC.label.judgeDetail")
    protected List<OSCRiskJudgeDtl> riskJudgeDtls = new ArrayList<OSCRiskJudgeDtl>();

}

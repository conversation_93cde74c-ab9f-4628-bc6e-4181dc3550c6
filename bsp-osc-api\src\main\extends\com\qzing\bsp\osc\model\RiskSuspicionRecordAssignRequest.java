package com.qzing.bsp.osc.model;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @version 1.0.0
 * <AUTHOR>
 * @Date 2025/7/28
 * @describe
 */
@Data
public class RiskSuspicionRecordAssignRequest {
    protected List<Long> ids;

    @Column(name = "assignedPersonCode")
    @FieldMetadata(name = "责任人员编码", resourceKey = "label.assignedPersonCode")
    protected String assignedPersonCode;

    @Column(name = "assignedPersonName")
    @FieldMetadata(name = "责任人员姓名", resourceKey = "label.assignedPersonName")
    protected String assignedPersonName;
}

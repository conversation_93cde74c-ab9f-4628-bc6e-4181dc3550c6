package com.qzing.bsp.osc.model;

import com.google.common.collect.Lists;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPageEvent;
import com.itextpdf.text.pdf.PdfWriter;
import com.qzing.ieep.util.CollectionUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

/**
 * pdf的文档类
 */
public class OSCPdfDocument {

    /**
     * doc对象
     */
    private Document document;

    /**
     * pdf路径
     */
    private String filePath;

    /**
     * 标题
     */
    private String title;

    /**
     * 作者
     */
    private String author;

    /**
     * 主题
     */
    private String subject;

    /**
     * 关键字
     */
    private String keywords;

    /**
     * 创建者
     */
    private String creator;


    /**
     * 事件集合，可以设置水印、页眉页脚
     */
    private List<PdfPageEvent> pdfPageEventList;

    /**
     * 要素，支持PdfPTable Paragraph 等
     */
    private List<Element> elementList;

    // 定义全局的字体静态变量
    private static Font titlefont;
    private static Font headfont;
    private static Font keyfont;
    private static Font textfont;
    // 最大宽度
    private static int maxWidth = 520;

    // 静态代码块
    static {
        try {
            // 不同字体（这里定义为同一种字体：包含不同字号、不同style）
            BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
            titlefont = new Font(bfChinese, 16, Font.BOLD);
            headfont = new Font(bfChinese, 14, Font.BOLD);
            keyfont = new Font(bfChinese, 10, Font.BOLD);
            textfont = new Font(bfChinese, 10, Font.NORMAL);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public OSCPdfDocument(Document document) {
        this.document = document;
    }

    /**
     * 创建pdf书写器
     *
     * @throws IOException
     * @throws DocumentException
     */
    private void createPdfWriter() throws IOException, DocumentException {
        File file = new File(filePath);
        if (!file.exists()) {
            Files.createDirectories(Paths.get(file.getParent()));
        }
        file.createNewFile();
        PdfWriter pdfWriter = PdfWriter.getInstance(this.document, new FileOutputStream(file));
        if (CollectionUtils.isEmpty(pdfPageEventList)) return;
        //设置页面事件
        pdfPageEventList.forEach(f -> {
            pdfWriter.setPageEvent(f);
        });
    }

    /**
     * 初始化一般信息
     */
    private void initGeneralInfo() {
        document.addTitle(title);
        document.addAuthor(author);
        document.addSubject(subject);
        document.addKeywords(keywords);
        document.addCreator(creator);
    }

    /**
     * 初始化内容
     */
    private void initContent() throws DocumentException {
        if (CollectionUtils.isEmpty(elementList)) {
            return;
        }
        for (Element element : elementList) {
            document.add(element);
        }
    }


    /**
     * 设置pdf的文件路径
     */
    public void setPdfFilePath(String filePath) {
        this.filePath = filePath;
    }

    /**
     * 设置一般信息
     *
     * @param title    标题
     * @param author   作者
     * @param subject  主题
     * @param keywords 关键字
     * @param creator  创建者
     */
    public void setGeneralInfo(String title, String author, String subject, String keywords, String creator) {
        this.title = title;
        this.author = author;
        this.subject = subject;
        this.keywords = keywords;
        this.creator = creator;
    }

    /**
     * 添加pdf页面事件
     *
     * @param pdfPageEvent
     */
    public void addPdfPageEvent(PdfPageEvent pdfPageEvent) {
        if (CollectionUtils.isEmpty(pdfPageEventList)) pdfPageEventList = Lists.newArrayList();
        pdfPageEventList.add(pdfPageEvent);
    }

    /**
     * 添加元素
     *
     * @param element
     */
    public void addElement(Element element) {
        if (CollectionUtils.isEmpty(elementList)) elementList = Lists.newArrayList();
        elementList.add(element);
    }

    /**
     * 生成文档
     */
    public void generate() throws IOException, DocumentException {
        //建立一个书写器(Writer)与document对象关联
        createPdfWriter();
        //打开文档
        document.open();
        //初始化一般信息
        initGeneralInfo();
        //向文档中添加内容
        initContent();
        //关闭文档
        document.close();
    }

}

package com.qzing.bsp.osc.util;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Font;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.qzing.bsp.osc.model.OSCPdfDocument;

import java.io.IOException;

/**
 * 生成PDF文件
 */
public class OSCPdfDocUtils {


    /**
     * 创建A4的Document
     *
     * @return
     */
    public static OSCPdfDocument createDocument() {
        return OSCPdfDocUtils.createDocument(PageSize.A4);
    }

    /**
     * 根据大小创建Document
     *
     * @param pageSize
     * @return
     */
    public static OSCPdfDocument createDocument(Rectangle pageSize) {
        Document document = new Document(pageSize);// 建立一个Document对象
        return new OSCPdfDocument(document);
    }

    /**
     * 获得标题字体
     *
     * @return
     */
    public static Font getTitleFont(float size, int style) throws IOException, DocumentException {
        BaseFont bfChinese = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
        return new Font(bfChinese, size, style);
    }

    /**
     * 获得标题段落
     *
     * @param context
     * @return
     * @throws IOException
     * @throws DocumentException
     */
    public static Paragraph getTitileParagraph(String context) throws IOException, DocumentException {
        //添加内容
        Paragraph titlelGaragraph = new Paragraph(getValue(context), getTitleFont(16, Font.BOLD));
        titlelGaragraph.setAlignment(Element.ALIGN_CENTER); //设置文字居中 0靠左   1，居中     2，靠右
        titlelGaragraph.setIndentationLeft(12); //设置左缩进
        titlelGaragraph.setIndentationRight(12); //设置右缩进
        titlelGaragraph.setFirstLineIndent(24); //设置首行缩进
        titlelGaragraph.setLeading(20f); //行间距
        titlelGaragraph.setSpacingBefore(5f); //设置段落上空白
        titlelGaragraph.setSpacingAfter(10f); //设置段落下空白
        return titlelGaragraph;
    }

    /**
     * 获得一个段落
     *
     * @param context
     * @return
     * @throws IOException
     * @throws DocumentException
     */
    public static Paragraph getParagraph(String context) throws IOException, DocumentException {
        //添加内容
        Paragraph titlelGaragraph = new Paragraph(getValue(context), getTitleFont(10, Font.NORMAL));
        return titlelGaragraph;
    }

    /**
     * 创建表格
     *
     * @param colNumber
     * @param align
     * @return
     */
    public static PdfPTable createTable(int colNumber, int align) {
        int maxWidth = 520;
        PdfPTable table = new PdfPTable(colNumber);
        table.setTotalWidth(maxWidth);
        table.setLockedWidth(true);
        table.setHorizontalAlignment(align);
        table.getDefaultCell().setBorder(1);
        return table;
    }

    /**
     * 创建无边框的单元格
     *
     * @param value
     * @return
     * @throws IOException
     * @throws DocumentException
     */
    public static PdfPCell createCellNoBorder(String value) throws IOException, DocumentException {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPhrase(new Phrase(getValue(value), getTitleFont(10, Font.NORMAL)));
        cell.setBorderWidthTop(0);
        cell.setBorderWidthRight(0);
        cell.setBorderWidthBottom(0);
        cell.setBorderWidthLeft(0);
        return cell;
    }

    /**
     * 创建单元格
     *
     * @param value
     * @return
     * @throws IOException
     * @throws DocumentException
     */
    public static PdfPCell createCell(String value) throws IOException, DocumentException {
        PdfPCell cell = new PdfPCell();
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPhrase(new Phrase(getValue(value), getTitleFont(10, Font.NORMAL)));
        return cell;
    }

    /**
     * 防止value为空处置
     *
     * @param value
     * @return
     */
    public static final String getValue(String value) {
        if (null == value) return "";
        return value;
    }

    //测试函数
    public static void main(String[] args) throws IOException, DocumentException {

        OSCPdfDocument imePdfDocument = OSCPdfDocUtils.createDocument();
        imePdfDocument.setGeneralInfo("测试", "Eason", "测试", "测试", "Eason");
        imePdfDocument.setPdfFilePath(System.getProperty("user.dir") + "/test.pdf");

        imePdfDocument.addElement(getTitileParagraph("你好！"));
        //生成代码
        imePdfDocument.generate();
    }
}

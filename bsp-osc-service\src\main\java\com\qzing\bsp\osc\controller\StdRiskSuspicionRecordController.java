package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.bsp.osc.model.RiskSuspicionRecordAssignRequest;
import com.qzing.bsp.osc.service.RiskSuspicionRecordService;
import com.qzing.ieep.coding.template.controller.SimpleControllerTemplate;
import com.qzing.ieep.data.common.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Api(tags = "风险疑点记录中间层接口")
//@Certificate(value = {"OSC"}, requiredType = RequiredType.ONE)
public class StdRiskSuspicionRecordController extends SimpleControllerTemplate<RiskSuspicionRecord, Long> {

    @Autowired
    protected RiskSuspicionRecordService service;

    @ApiOperation(value = "分配人员")
    @PostMapping("/assign")
    public RestResponse<?> assign(@RequestBody RiskSuspicionRecordAssignRequest request) {
        service.assign(request);
        return RestResponse.success();
    }

    @ApiOperation("根据单据编码获取单据详情")
    @GetMapping({"/get"})
    public RestResponse<?> get(@RequestParam("billNo") String billNo) {
        return RestResponse.success(this.service.getBill(billNo));
    }
}

package com.qzing.bsp.osc.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.dao.RiskSuspicionRecordDtlDao;
import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.bsp.osc.entity.RiskSuspicionRecordDtl;
import com.qzing.bsp.osc.enums.OSCState;
import com.qzing.bsp.osc.model.RiskSuspicionRecordAssignRequest;
import com.qzing.bsp.osc.util.OSCFieldRequiredUtils;
import com.qzing.core.sys.api.BillSetServiceClient;
import com.qzing.ieep.coding.template.service.SimpleServiceImplTemplate;
import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.util.DataUtils;
import com.qzing.ieep.event.listener.AbstractTransactionEventCallback;
import com.qzing.ieep.event.service.TransactionEventPublisher;
import com.qzing.ieep.file.api.util.FileInfoUtils;
import com.qzing.ieep.notify.client.NotifySenderClient;
import com.qzing.ieep.notify.dto.NotifySenderParam;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.context.i18n.LocaleContextHolder.getLocale;

/**
 * 风险疑点记录服务实现类
 */
@ModuleInfo(moduleCode = OSCBillType.FXYD)
public class StdRiskSuspicionRecordServiceImpl extends SimpleServiceImplTemplate<RiskSuspicionRecord, Long> implements StdRiskSuspicionRecordService {
    @Autowired
    protected RiskSuspicionRecordDtlDao riskSuspicionRecordDtlDao;
    @Autowired(required = false)
    protected BillSetServiceClient billSetServiceClient;
    @Autowired(required = false)
    protected TransactionEventPublisher eventPublisher;
    @Autowired(required = false)
    protected NotifySenderClient notifySenderClient;

    @Override
    protected void beforeSave(RiskSuspicionRecord entity, Map<String, Object> extraMap) {
        entity.setRiskSuspicionRecordNo(billSetServiceClient.createNextRunningNum(OSCBillType.FXYD));
        entity.setSource("manual");
        entity.setStatus(OSCState.NEW);
    }

    @Override
    protected void afterSave(RiskSuspicionRecord entity, Map<String, Object> extraMap) {
        //给负责人员发送待办
        if (StrUtil.isNotBlank(entity.getAssignedPersonCode())) {
            sendMessage("", entity, extraMap, StrUtil.split(entity.getAssignedPersonCode(), ","));
        }
    }

    @Override
    public void validate(RiskSuspicionRecord entity, Map<String, Object> extraMap) {
        OSCFieldRequiredUtils.validFieldValue(DataUtils.parseMap(entity), new String[]{"companyCode", "companyName", "assignedPersonCode", "assignedPersonName"}, getLocale());
        for (RiskSuspicionRecordDtl dtl : entity.getSuspicionDetails()) {
            OSCFieldRequiredUtils.validFieldValue(DataUtils.parseMap(dtl), new String[]{"suspicionDesc"}, getLocale());
        }
    }

    protected void sendMessage(String notifyCode, RiskSuspicionRecord po, Map<String, Object> extraMap, List<String> notifyUsers) {
        eventPublisher.afterCommit(this, po, new AbstractTransactionEventCallback<RiskSuspicionRecord>() {
            @Override
            public void handle(RiskSuspicionRecord po) {
                HashMap<String, Object> map = DataUtils.parseMainMap(po);
                map.put("billNo", po.getRiskSuspicionRecordNo());
                NotifySenderParam notifyParam = NotifySenderParam.initBuild()
                        // 消息模板编码
                        .notifyCode(notifyCode)
                        // 额外参数
                        .extraParams(map)
                        // 发送者
                        .senderCode(CurrentContextHelper.getUserCode())
                        // 发送者
                        .senderName(CurrentContextHelper.getUserName())
                        // 单据类型
                        .billType(OSCBillType.FXYD)
                        // 单据id
                        .billId(po.getRiskSuspicionRecordId().toString())

                        .receiverCodes(notifyUsers)
                        // 发送消息
                        .buildForPush();

                notifySenderClient.send(notifyParam);
            }
        });
    }

    @Override
    protected void setEntityRelation(RiskSuspicionRecord entity) {
        Long fileGroupId = this.bindFileGroup(entity.getFileGroupId(), entity.getFileInfoList());
        entity.setFileGroupId(fileGroupId);
        entity.setDtlCount(entity.getSuspicionDetails().size());
        for (RiskSuspicionRecordDtl dtl : entity.getSuspicionDetails()) {
            dtl.setRiskSuspicionRecord(entity);
            dtl.setSecretLevel(entity.getSecretLevel());
            fileGroupId = this.bindFileGroup(dtl.getFileGroupId(), dtl.getFileInfoList());
            dtl.setFileGroupId(fileGroupId);
        }
    }

    @Override
    public void assign(RiskSuspicionRecordAssignRequest request) {
        OSCFieldRequiredUtils.validFieldValue(DataUtils.parseMap(request), new String[]{"ids", "assignedPersonCode", "assignedPersonName"}, getLocale());
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("IN_riskSuspicionRecordId", request.getIds());
        List<RiskSuspicionRecord> list = findAll(searchMap);
        for (RiskSuspicionRecord record : list) {
            record.setAssignedPersonCode(request.getAssignedPersonCode());
            record.setAssignedPersonName(request.getAssignedPersonName());
            record.setStatus(OSCState.ALLOCATED);
            save(record);
            addLog(record, "buttom.distribution");
        }
    }

    /**
     * 研判引用/与解绑回写接口
     */
    @Override
    public void writeBackByRJS(Map<String, List<Long>> oldDtlMap, Map<String, List<Long>> newDtlMap, Integer isBind) {
        Map<String, Object> searchMap = new HashMap<>();
        // 如果oldDtlMap不为空，说明是编辑回写的场景，需要把oldDtl对应的id置为0
        if (null != oldDtlMap) {
            for(Map.Entry<String, List<Long>> entry: oldDtlMap.entrySet()) {
                searchMap.clear();
                String riskSuspicionRecordNo = entry.getKey();
                List<Long> dtlIdList = entry.getValue();
                searchMap.put("riskSuspicionRecordNo", riskSuspicionRecordNo);
                RiskSuspicionRecord suspicionRecord = findOne(searchMap);
                if (null == suspicionRecord) {
                    BizException.throwEx(getText("common.valid.message.notexisted"));
                }
                List<RiskSuspicionRecordDtl> dtls = suspicionRecord.getSuspicionDetails();
                int sepSize = 0;
                for (RiskSuspicionRecordDtl dtl : dtls) {
                    if (dtlIdList.contains(dtl.getRiskSuspicionRecordDtlId())) {
                        dtl.setIsGenerateJudge(0);
                    }
                    if (Integer.valueOf(1).equals(dtl.getIsGenerateJudge())) {
                        sepSize++;
                    }
                }
                if (sepSize == 0) {
                    suspicionRecord.setStatus(OSCState.ALLOCATED);
                } else if (sepSize == dtls.size()) {
                    suspicionRecord.setStatus(OSCState.WQYP);
                } else {
                    suspicionRecord.setStatus(OSCState.BFYP);
                }
                save(suspicionRecord);
            }
        }

        if (null != newDtlMap) {
            for(Map.Entry<String, List<Long>> entry: newDtlMap.entrySet()) {
                searchMap.clear();
                String riskSuspicionRecordNo = entry.getKey();
                searchMap.put("riskSuspicionRecordNo", riskSuspicionRecordNo);
                RiskSuspicionRecord suspicionRecord = findOne(searchMap);
                if (null == suspicionRecord) {
                    BizException.throwEx(getText("common.valid.message.notexisted"));
                }
                List<Long> dtlIdList = entry.getValue();
                List<RiskSuspicionRecordDtl> dtls = suspicionRecord.getSuspicionDetails();
                int sepSize = 0;
                for (RiskSuspicionRecordDtl dtl : dtls) {
                    if (dtlIdList.contains(dtl.getRiskSuspicionRecordDtlId())) {
                        dtl.setIsGenerateJudge(isBind);
                    }
                    if (Integer.valueOf(1).equals(dtl.getIsGenerateJudge())) {
                        sepSize++;
                    }
                }
                if (sepSize == 0) {
                    suspicionRecord.setStatus(OSCState.ALLOCATED);
                } else if (sepSize == dtls.size()) {
                    suspicionRecord.setStatus(OSCState.WQYP);
                } else {
                    suspicionRecord.setStatus(OSCState.BFYP);
                }
                save(suspicionRecord);
            }
        }

    }

    @Override
    public RiskSuspicionRecord getBill(String billNo) {
        RiskSuspicionRecord riskSuspicionRecord = get(billNo);
        FileInfoUtils.buildFileInfo(riskSuspicionRecord);
        return riskSuspicionRecord;
    }

    @Override
    public RestResponse importDetailData(List<RiskSuspicionRecordDtl> requests) {
        Map<String, Object> errorMap = new HashMap<>();
        for (RiskSuspicionRecordDtl request : requests) {
            if (StrUtil.isBlank(request.getSuspicionDesc())) {
                errorMap.put(request.getTemporaldataId(), getText("suspiciousDesc.notallownull"));
            }
        }
        if (!errorMap.isEmpty()) {
            return RestResponse.error(JSONObject.toJSONString(errorMap));
        }
        return RestResponse.success();
    }
}

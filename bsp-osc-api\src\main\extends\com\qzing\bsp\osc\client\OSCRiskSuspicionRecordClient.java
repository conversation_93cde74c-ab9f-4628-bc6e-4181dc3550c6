package com.qzing.bsp.osc.client;

import com.qzing.bsp.osc.entity.RiskSuspicionRecordDtl;
import com.qzing.ieep.data.common.RestResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 风险疑点记录中台接口
 *
 * <AUTHOR>
 */
@FeignClient(name = "${bsp.service.osc.name}${bsp.service.osc.version:}/${bsp.service.osc.version}/oscrisksuspicionrecord")
public interface OSCRiskSuspicionRecordClient {

    /**
     * 风险疑点记录明细导入
     *
     * @param requests
     * @return
     */
    @PostMapping("/importDetailData")
    RestResponse importDetailData(@RequestBody List<RiskSuspicionRecordDtl> requests);


}

package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.annotation.BizNumber;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.util.Calendar;

/**
 * 场景人员关系实体类-基类
 */
@Data
@MappedSuperclass
public class StdSceneUserRelation extends OSCEntity<Long> {

    private static final long serialVersionUID = 2526335682158437862L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "sceneUserRelationId")
    @FieldMetadata(name = "关系ID", resourceKey = "label.sceneUserRelationId", skipHistoryComparison = true)
    protected Long sceneUserRelationId;


    @Column(name = "sceneCode")
    @FieldMetadata(name = "场景编码", resourceKey = "label.sceneCode")
    protected String sceneCode;

    @Column(name = "sceneName")
    @FieldMetadata(name = "场景名称", resourceKey = "label.sceneName")
    protected String sceneName;

    @Column(name = "userCode")
    @FieldMetadata(name = "人员编码", resourceKey = "label.userCode")
    protected String userCode;

    @Column(name = "userName")
    @FieldMetadata(name = "人员姓名", resourceKey = "label.userName")
    protected String userName;


    @Column(name = "status")
    @FieldMetadata(resourceKey = "label.quality.initiateMode")
    private Integer status;  // 启用状态

}

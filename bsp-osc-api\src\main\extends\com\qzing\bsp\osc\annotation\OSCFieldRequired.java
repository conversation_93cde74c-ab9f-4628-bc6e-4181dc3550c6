package com.qzing.bsp.osc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 必填字段注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface OSCFieldRequired {

    /**
     * 自定义错误消息
     */
    String message() default "";

    /**
     * 校验分组
     */
    Class<?>[] groups() default {};

    /**
     * 是否递归校验（对对象字段有效）
     */
    boolean recursive() default true;

    /**
     * 校验的动作范围
     * @return
     */
    String[] area() default {};

}

package com.qzing.bsp.osc.service;

import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.entity.OSCTaskDtl;
import com.qzing.bsp.osc.entity.OSCTaskProcessRecord;
import com.qzing.ieep.coding.template.service.ServiceTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;

import java.util.List;


/**
 * 任务管理
 */
public interface StdOSCTaskService extends ServiceTemplate<OSCTask, Long> {

    /**
     * 获取详情
     * @param request
     * @return
     */
    OSCTask get(OSCTask request);

    /**
     * 获取明细不分页
     * @param request
     * @return
     */
    List<OSCTaskDtl> getDetail(OSCTask request);

    /**
     * 执行记录列表
     * @param request
     * @return
     */
    List<OSCTaskProcessRecord> findProcessRecordList(JpaSearchRequest request);

    /**
     * 接受	任务负责人接受任务
     * @return
     */
    RestResponse accept(OSCTask request);

    /**
     * 拒绝	任务负责人拒绝任务
     * @return
     */
    RestResponse refuse(OSCTask request);

    /**
     * 派单	指派任务负责人，并且发送消息通知
     * @param request
     * @return
     */
    RestResponse reDispatchDtl(OSCTaskDtl request);

    /**
     * 任务完成	任务负责人完成任务，通知
     * @return
     */
    RestResponse finish(OSCTask request);

    /**
     * 任务完成	任务执行人完成任务，通知
     * @return
     */
    RestResponse finishDtl(OSCTaskDtl request);

    /**
     * 关闭	创建人进行验收，没有问题，关闭该任务
     * @return
     */
    RestResponse close(OSCTaskDtl request);

    /**
     * 激活	创建人进行验收，有疑问激活任务
     * @param request
     * @return
     */
    RestResponse activation(OSCTaskDtl request);

    /**
     * 催办	支持风控人对任务负责人进行催办提醒
     * @param request
     * @return
     */
    RestResponse urging(OSCTask request);

    /**
     * 催办	对任务执行人进行催办提醒
     * @param request
     * @return
     */
    RestResponse urgingDtl(OSCTaskDtl request);

    /**
     * 任务预警	任务快到期，还没有完成，支持提前预警
     * @param request
     * @return
     */
    RestResponse warning(OSCTaskDtl request);

    /**
     * 挂起	支持任务挂起，后续在激活执行
     * @param request
     * @return
     */
    RestResponse pending(OSCTaskDtl request);

//    编辑	当任务无法按时完成时，也支持调整任务计划

//    取消	任务因为种种原因无法执行，支持进行取消

    //删除	还未分派的任务支持手工删除


}

package com.qzing.bsp.osc.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Element;
import com.itextpdf.text.Image;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.BarcodeQRCode;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.qzing.bsp.osc.constant.EightDReportConstant;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.dao.OSCEightDReportRecordDao;
import com.qzing.bsp.osc.entity.OSCEightDReport;
import com.qzing.bsp.osc.entity.OSCEightDReportDetail;
import com.qzing.bsp.osc.entity.OSCEightDReportRc;
import com.qzing.bsp.osc.entity.OSCEightDReportRecord;
import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.enums.OSCEightDReportEnum;
import com.qzing.bsp.osc.enums.ImproveObjectEnum;
import com.qzing.bsp.osc.model.EightDReportDispatchRequest;
import com.qzing.bsp.osc.model.OSCEightDReportEdit;
import com.qzing.bsp.osc.model.EightDReportUpdateRequest;
import com.qzing.bsp.osc.model.OSCPdfDocument;
import com.qzing.bsp.osc.util.OSCFieldRequiredUtils;
import com.qzing.bsp.osc.util.OSCFileUtils;
import com.qzing.bsp.osc.util.OSCPdfDocUtils;
import com.qzing.ieep.coding.template.service.ServiceImplTemplate;
import com.qzing.ieep.context.CurrentContextHelper;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.util.vo.FileInfoResponse;
import com.qzing.ieep.file.api.FileInfoClient;
import com.qzing.ieep.logging.entity.OperateLog;
import com.qzing.ieep.logging.listener.AuditListener;
import com.qzing.ieep.logging.util.OperateLogUtils;
import com.qzing.ieep.notify.client.NotifySenderClient;
import com.qzing.ieep.util.CollectionUtils;
import com.qzing.ui.core.service.DictValueExtService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class StdOSCEightDReportServiceImpl extends ServiceImplTemplate<OSCEightDReport, Long> implements StdOSCEightDReportService {

    @Autowired
    protected NotifySenderClient notifySenderClient;
    @Autowired
    protected FileInfoClient fileInfoClient;
    @Autowired
    private OSCEightDReportRecordDao eightDReportRecordDao;
    @Autowired
    protected DictValueExtService dictValueExtService;

    public void record(OSCEightDReport report, String operator, OSCEightDReportEnum reportStateEnum) {
        OSCEightDReportRecord maxRecord = getPreNode(report.getReportId());
        OSCEightDReportRecord record = new OSCEightDReportRecord();
        record.setReportId(report.getReportId());
        record.setBillStatus(reportStateEnum.name());
        record.setOperateTime(Calendar.getInstance());
        record.setOperator(StringUtils.isEmpty(operator) ? CurrentContextHelper.getUserCode() : operator);
        record.setOperateNum(ObjectUtils.isEmpty(maxRecord) || ObjectUtils.isEmpty(maxRecord.getOperateNum()) ? 1 : maxRecord.getOperateNum() + 1);
        eightDReportRecordDao.save(record);
    }

    public OSCEightDReportRecord getPreNode(Long reportGid) {
        return eightDReportRecordDao.findFirstByReportIdOrderByOperateNumDesc(reportGid);
    }

    /**
     * 由任务单创建
     * @param task
     */
    public void saveFromTask(OSCTask task) {
        OSCEightDReport eightDReport = new OSCEightDReport();
        //设置任务信息
        eightDReport.setTask(task);
        eightDReport.setTaskNo(task.getTaskNo());
        eightDReport.setClientCode(task.getClientCode());
        //权限字段
        eightDReport.setDepartmentCode(task.getDepartmentCode());
        eightDReport.setDepartmentName(task.getDepartmentName());
        eightDReport.setCompanyCode(task.getCompanyCode());
        eightDReport.setCompanyName(task.getCompanyName());
        //责任人信息
        eightDReport.setTaskOwnerCode(task.getTaskOwnerCode());
        eightDReport.setTaskOwnerName(task.getTaskOwnerName());
        eightDReport.setOwnerUserId(task.getOwnerUserId());
        //设置状态
        setStatus(eightDReport, OSCEightDReportEnum.D0ProblemPropose);
        save(eightDReport);
        //日志
        addLog(eightDReport, "save", null);
        //记录提交操作
        record(eightDReport, null, OSCEightDReportEnum.D0ProblemPropose);
        log.info("8D改进管理新增成功: {}", eightDReport.getTaskNo());
        //发送消息 todo
//        imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(report.getDutyDeptUserCode()), EightDReportConstant.Notify.TODO, buildMsgParam(report), report.getGid().toString());
    }

    /**
     * 日志
     * @param entity
     * @param action
     * @param message
     */
    protected void addLog(OSCEightDReport entity, String action, String message) {
        OperateLog operateLog = OperateLogUtils.OperateLogBuilder.builder()
                .action(action)
                .moduleCode(CurrentContextHelper.getModuleCode())
                .bizkey(entity.getReportId().toString())
                .terminal(CurrentContextHelper.getTerminal())
                .operatorHost(CurrentContextHelper.getUserRealIp())
                .operatorId(CurrentContextHelper.getUserId())
                .operatorName(CurrentContextHelper.getUserName())
                .requestId(CurrentContextHelper.getRequestId())
                .requestTime(CurrentContextHelper.getRequestTime())
                .responseTime(Calendar.getInstance())
                .businessNo(entity.getTaskNo())
                .module(OSCBillType.EightDReport)
                .message(message)
                .build();
        //历史记录
        operateLog.setHistories(AuditListener.getModifyHistories(entity));
        OperateLogUtils.log(operateLog);
    }

    /**
     * 8D改进管理问题提出编辑
     * @param request
     */
    @Override
    public void problemProposeSave(EightDReportUpdateRequest request) {
        OSCEightDReport report = problemPropose(request, false);
        report.setD0FileGroupGid(bindFileGroup(request.getD0FileGroupGid(), request.getD0FileInfoList()));
    }

    /**
     * 8D改进管理问题提出提交
     * @param request
     */
    @Override
    public void problemProposeSubmit(EightDReportUpdateRequest request) {
        OSCEightDReport report = problemPropose(request, true);
        report.setD0FileGroupGid(bindFileGroup(request.getD0FileGroupGid(), request.getD0FileInfoList()));
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D0ProblemPropose);
        //发送消息
        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(request.getImproveObject())) {
//            imeNotifySenderServiceClient.sendToSupper(report.getVendorCode(), EightDReportConstant.Notify.SUPPLIER_TODO, buildMsgParam(report), report.getGid().toString());
        } else if (ImproveObjectEnum.INTERNAL_IMPROVE.name().equals(request.getImproveObject())) {
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(report.getDutyDeptUserCode()), EightDReportConstant.Notify.TODO, buildMsgParam(report), report.getGid().toString());
        }
    }

    /**
     * 问题提出
     *
     * @param request
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport problemPropose(EightDReportUpdateRequest request, boolean bSubmit) {
        //参数校验
        OSCFieldRequiredUtils.validateAndThrow(request, "problemPropose");
        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(request.getImproveObject())) {
            //供应商改进校验编码
//            this.baseValid(request, EightDReportConstant.QUALITY_EIGHTDREPORT_SAVE_SUPPLIER);
        } else if (ImproveObjectEnum.INTERNAL_IMPROVE.name().equals(request.getImproveObject())) {
            //内部改进校验编码
//            this.baseValid(request, EightDReportConstant.QUALITY_EIGHTDREPORT_SAVE_INTERNAL);
        }

        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            BizException.throwEx(getText("common.valid.dataNotExist"));
        }
        //检验修改时间
//        checkModifyTime(report, request.getModifyTime());
        //校验状态
        boolean checkStatus = checkStatus(report, OSCEightDReportEnum.D0ProblemPropose);
        if(!checkStatus) {
            //状态不为{0},无法操作！
            throwStatusNotAllow("D0问题提出");
        }
        if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D1GroupSetup);
        }
        report.setD0FileGroupGid(bindFileGroup(request.getD0FileGroupGid(), request.getD0FileInfoList()));
        //修改时间，以免只修改rc修改时间不准确
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 状态不为{0},无法操作！
     * @param message
     */
    public void throwStatusNotAllow(String message) {
        BizException.throwEx(getText("ime.quality.eightdreport.status.not").replace("{0}", message));
    }

    /**
     * 构建发送消息的参数
     *
     * @param report
     * @return
     */
    protected Map<String, Object> buildMsgParam(OSCEightDReport report) {
        Map<String, Object> extraParams = Maps.newHashMap();
        extraParams.put("billNo", report.getTaskNo());
        extraParams.put("billId", report.getReportId());
        extraParams.put("billType", OSCBillType.EightDReport);
        return extraParams;
    }

    /**
     * 得到步骤操作用户
     *
     * @param report
     * @param step
     * @return
     */
    protected String getStepUserCode(OSCEightDReport report, OSCEightDReportEnum step) {
        Map<String, OSCEightDReportDetail> mapStepUserCode = report.getEightDReportDetailList().stream().collect(Collectors.toMap(OSCEightDReportDetail::getProcessStep, a -> a, (k1, k2) -> k1));
        if (null == mapStepUserCode) {
            return null;
        }
        if (!mapStepUserCode.containsKey(step.name())) {
            return null;
        }
        return mapStepUserCode.get(step.name()).getUserCode();
    }

    /**
     * 8D改进管理小组成立编辑保存
     * @param request
     * @param checkStatus
     */
    @Override
    public void groupSetupSave(EightDReportDispatchRequest request, boolean checkStatus) {
        groupSetup(request, checkStatus, false);
    }

    /**
     * 8D改进管理小组成立编辑提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void groupSetupSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = groupSetup(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D1GroupSetup);
    }

    /**
     * 抛异常-参数为空
     */
    public void throwParamIsBlank() {
        BizException.throwEx(getText("common.valid.request.noBlank"));
    }

    /**
     * 小组提交封装
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     */
    protected OSCEightDReport groupSetup(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        if(request == null || request.getReportId() == null) {
            throwParamIsBlank();
        }
        if(request.getIsAcceptProblem() == null) {
            //是否接收问题不能为空！
            BizException.throwEx(getText("ime.quality.eightdreport.isAcceptProblem.notnull"));
        }
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus) {
            checkStatus(report, OSCEightDReportEnum.D1GroupSetup);
        }

        //如果拒绝问题，需要校验理由
        if (!request.getIsAcceptProblem() && bSubmit) {
            OSCFieldRequiredUtils.validateAndThrow(request, "groupSetup");
        }
        //如果为内部改进且接受问题，校验小组的合法性
        if (request.getIsAcceptProblem()) {
            if(CollectionUtils.isEmpty(request.getGroupMembersRequestList())) {
                BizException.throwEx(getText("ime.quality.eightdreport.groupMembersRequestList.notnull"));
            }
            request.getGroupMembersRequestList().forEach(members -> {
                //校验成员必填
                OSCFieldRequiredUtils.validateAndThrow(members);
            });
            boolean checkProcessStep = checkProcessStep(request.getGroupMembersRequestList());
            if(checkProcessStep) {
                //组成员需包括D2-D8办理人！
                BizException.throwEx(getText("ime.quality.eightdreport.handler.notInclude"));
            }
        }

        //校验操作用户的合法性
        checkOperator(report, OSCEightDReportEnum.D1GroupSetup);

        //赋值小组人员
        if (!CollectionUtils.isEmpty(request.getGroupMembersRequestList()))
            report.setEightDReportDetailList(request.getGroupMembersRequestList());
            setEntityRelation(report);
            report.getEightDReportRc().setIsAcceptProblem(request.getIsAcceptProblem());
            report.getEightDReportRc().setAcceptRefuseReason(request.getAcceptRefuseReason());
        //是否接收问题
        if (request.getIsAcceptProblem() && bSubmit) {
            setStatus(report, OSCEightDReportEnum.D2ProblemDefine);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D2ProblemDefine)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        }

        if (!request.getIsAcceptProblem() && bSubmit) {
            setStatus(report, OSCEightDReportEnum.D0ProblemPropose);
            //发送消息给创建着 todo
//            imeNotifySenderServiceClient.sendToUserId(Lists.newArrayList(report.getCreateUserId()),
//                    getBackByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        }
        //修改时间
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    @Override
    protected void setEntityRelation(OSCEightDReport report) {
        if(!CollectionUtils.isEmpty(report.getEightDReportDetailList())) {
            for (OSCEightDReportDetail detail : report.getEightDReportDetailList()) {
                detail.setEightDReport(report);
            }
        }
    }


    public boolean checkProcessStep(List<OSCEightDReportDetail> list){
        List<String> allStepList = getStepNotContain(list);
        return allStepList != null && allStepList.size() <= 0;
    }

    public List<String> getStepNotContain(List<OSCEightDReportDetail> list){
        List<String> stepList = list.stream().filter(r->!StringUtils.isEmpty(r.getProcessStep()))
                .map(OSCEightDReportDetail::getProcessStep).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stepList)){
            return null;
        }
        List<String> allStepList = new ArrayList<>();
        allStepList.add(OSCEightDReportEnum.D2ProblemDefine.name());
        allStepList.add(OSCEightDReportEnum.D3ContainmentMeasure.name());
        allStepList.add(OSCEightDReportEnum.D4ReasonAnalysis.name());
        allStepList.add(OSCEightDReportEnum.D5CorrectMeasure.name());
        allStepList.add(OSCEightDReportEnum.D6EffectVerify.name());
        allStepList.add(OSCEightDReportEnum.D7RreventMeasures.name());
        allStepList.add(OSCEightDReportEnum.D8ClosedEvaluate.name());

        allStepList.removeAll(stepList);
        return allStepList;
    }

    /**
     * 8D改进管理问题界定编辑
     * @param request
     * @param checkStatus
     */
    @Override
    public void problemDefineSave(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = problemDefine(request, checkStatus, false);
    }

    /**
     * 8D改进管理问题界定提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void problemDefineSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = problemDefine(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D2ProblemDefine);
        //发送消息              todo
//        imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D3ContainmentMeasure)),
//                getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
    }


    /**
     * 问题定界
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     */
    protected OSCEightDReport problemDefine(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        if(StringUtils.isBlank(request.getProblemDefine())) {
            //问题界定不能为空!
            BizException.throwEx(getText("ime.quality.eightdreport.problemDefine.notnull"));
        }
        //检查状态
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus) {
            boolean status = checkStatus(report, OSCEightDReportEnum.D2ProblemDefine);
            if(!status) {
                //状态不为{0},无法操作！
                throwStatusNotAllow("D2问题界定");
            }
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D2ProblemDefine);

        if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D3ContainmentMeasure);
        }
        if (ObjectUtils.isEmpty(report.getEightDReportRc())) {
            report.setEightDReportRc(new OSCEightDReportRc());
        }
        report.getEightDReportRc().setProblemDefine(request.getProblemDefine());
        //附件
        report.getEightDReportRc().setD2FileGroupGid(this.bindFileGroup(request.getD2FileGroupGid(), request.getD2FileInfoList()));
        //修改时间
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 8D改进管理围堵措施编辑
     * @param request
     * @param checkStatus
     */
    @Override
    public void containmentMeasureSave(EightDReportDispatchRequest request, boolean checkStatus) {
        containmentMeasure(request, checkStatus, false);
    }

    /**
     * 8D改进管理围堵措施提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void containmentMeasureSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = containmentMeasure(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D3ContainmentMeasure);
    }

    /**
     * 围堵措施
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport containmentMeasure(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //参数校验
        if(request == null || request.getD3DirectClosure() == null) {
            //D3直接结案不能为空
            BizException.throwEx(getText("ime.quality.eightdreport.d3DirectClosure.notnull"));
        }
        if (request.getD3DirectClosure() && bSubmit) {
            OSCFieldRequiredUtils.validateAndThrow(request, "D3_TRUE");
        } else if (bSubmit) {
            OSCFieldRequiredUtils.validateAndThrow(request, "D3_FALSE");
        }
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus && bSubmit) {
            checkStatus(report, OSCEightDReportEnum.D3ContainmentMeasure);
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D3ContainmentMeasure);

        //判断是否直接结案
        if (request.getD3DirectClosure() && bSubmit) {
            setStatus(report, OSCEightDReportEnum.D8ClosedEvaluate);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D8ClosedEvaluate)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        } else if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D4ReasonAnalysis);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D4ReasonAnalysis)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        }
        //赋值
        report.getEightDReportRc().setContainmentMeasure(request.getContainmentMeasure());
        report.getEightDReportRc().setD3DirectClosure(request.getD3DirectClosure());
        report.getEightDReportRc().setD3DirectClosureReason(request.getD3DirectClosureReason());
        //修改时间
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 原因分析保存
     * @param request
     * @param checkStatus
     */
    @Override
    public void reasonAnalysisSave(EightDReportDispatchRequest request, boolean checkStatus) {
        reasonAnalysis(request, checkStatus, false);
    }

    /**
     * 原因分析提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void reasonAnalysisSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = reasonAnalysis(request, checkStatus, true);
        record(report, null, OSCEightDReportEnum.D4ReasonAnalysis);
        //发送消息      todo
//        imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D5CorrectMeasure)),
//                getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
    }

    /**
     * 原因分析
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport reasonAnalysis(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //参数校验
        OSCFieldRequiredUtils.validateAndThrow(request, "reasonAnalysis");

        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus) {
            checkStatus(report, OSCEightDReportEnum.D4ReasonAnalysis);
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D4ReasonAnalysis);

        report.getEightDReportRc().setReasonAnalysis(request.getReasonAnalysis());
        //附件
        report.getEightDReportRc().setD4FileGroupGid(this.bindFileGroup(request.getD4FileGroupGid(), request.getD4FileInfoList()));
        if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D5CorrectMeasure);
        }
        //修改时间，以免只修改rc修改时间不准确
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 纠正措施保存
     * @param request
     * @param checkStatus
     */
    @Override
    public void correctMeasureSave(EightDReportDispatchRequest request, boolean checkStatus) {
        correctMeasure(request, checkStatus, false);
    }

    /**
     * 纠正措施提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void correctMeasureSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = correctMeasure(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D5CorrectMeasure);
        //发送消息           todo
//        imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D6EffectVerify)),
//                getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
    }

    /**
     * 纠正措施
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport correctMeasure(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //参数校验
        OSCFieldRequiredUtils.validateAndThrow(request, "correctMeasure");
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus) {
            checkStatus(report, OSCEightDReportEnum.D5CorrectMeasure);
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D5CorrectMeasure);

        //赋值
        report.getEightDReportRc().setCorrectMeasure(request.getCorrectMeasure());
        report.getEightDReportRc().setExecutor(request.getExecutor());
        report.getEightDReportRc().setExecuteTime(request.getExecuteTime());
        // 20220616 附件调整
        report.getEightDReportRc().setD5FileGroupGid(this.bindFileGroup(request.getD5FileGroupGid(), request.getD5FileInfoList()));
        if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D6EffectVerify);
            report.getEightDReportRc().setCorrectMeasureDate(Calendar.getInstance());
        }
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 效果验证保存
     * @param request
     * @param checkStatus
     */
    @Override
    public void effectVerifySave(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = effectVerify(request, checkStatus, false);
    }

    /**
     * 效果验证提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void effectVerifySubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = effectVerify(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D6EffectVerify);
    }

    /**
     * 效果验证
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport effectVerify(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //参数校验 quality_eightdreport_effectverifysave
        if (bSubmit) {
            OSCFieldRequiredUtils.validateAndThrow(request, "effectVerify");
        }
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus && bSubmit){
            //D6
            checkStatus(report, OSCEightDReportEnum.D6EffectVerify);
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D6EffectVerify);

        //是否有效
        if (bSubmit && request.getIsEffective()) {
            setStatus(report, OSCEightDReportEnum.D7RreventMeasures);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D7RreventMeasures)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        } else if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D4ReasonAnalysis);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D4ReasonAnalysis)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        }
        //赋值
        report.getEightDReportRc().setEffectVerify(request.getEffectVerify());
        report.getEightDReportRc().setIsEffective(request.getIsEffective());
        report.getEightDReportRc().setVerifier(request.getVerifier());
        report.getEightDReportRc().setVerifyTime(request.getVerifyTime());
        //附件
        report.getEightDReportRc().setD6FileGroupGid(this.bindFileGroup(request.getD6FileGroupGid(), request.getD6FileInfoList()));
        //修改时间
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 预防再发生保存
     * @param request
     * @param checkStatus
     */
    @Override
    public void preventMeasuresSave(EightDReportDispatchRequest request, boolean checkStatus) {
        preventMeasures(request, checkStatus, false);
    }

    /**
     * 预防再发生提交
     * @param request
     * @param checkStatus
     */
    @Override
    public void preventMeasuresSubmit(EightDReportDispatchRequest request, boolean checkStatus) {
        OSCEightDReport report = preventMeasures(request, checkStatus, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D7RreventMeasures);
        //发送消息         todo
//        imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D8ClosedEvaluate)),
//                getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
    }

    /**
     * 预防再发生
     *
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport preventMeasures(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //校验参数
        OSCFieldRequiredUtils.validateAndThrow(request, "preventMeasures");
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus && bSubmit) {
            boolean status = checkStatus(report, OSCEightDReportEnum.D7RreventMeasures);
            if(!status) {
                throwStatusNotAllow("D7预防再发生");
            }
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D7RreventMeasures);

        if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.D8ClosedEvaluate);
        }
        //赋值
        report.getEightDReportRc().setPreventMeasures(request.getPreventMeasures());
        report.getEightDReportRc().setD7FileGroupGid(this.bindFileGroup(request.getD7FileGroupGid(), request.getD7FileInfoList()));
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    /**
     * 抛异常-操作人不满足
     * @param message
     */
    public void throwHandlerNotAllow(String message) {
        BizException.throwEx(getText("ime.quality.eightdreport.handler.is").replace("{0}", message));
    }

    /**
     * 抛异常-操作节点不满足
     */
    public void throwNodeNotAllow() {
        //不允许跨节点填写8D报告，请检查！
        BizException.throwEx(getText("ime.quality.eightdreport.node.notAllowed"));
    }

    /**
     * 8D改进管理结案评价编辑
     * @param request
     * @param checkStatus
     */
    @Override
    public void closedEvaluateSave(EightDReportDispatchRequest request, boolean checkStatus) {
        closedEvaluate(request, checkStatus, false);
    }

    /**
     * 8D改进管理结案评价提交
     * @param request
     */
    @Override
    public void closedEvaluateSubmit(EightDReportDispatchRequest request) {
        OSCEightDReport report = closedEvaluate(request, true, true);
        //记录提交操作
        record(report, null, OSCEightDReportEnum.D8ClosedEvaluate);
    }

    /**
     * 8D改进管理结案评价
     * @param request
     * @param checkStatus
     * @param bSubmit
     * @return
     */
    protected OSCEightDReport closedEvaluate(EightDReportDispatchRequest request, boolean checkStatus, boolean bSubmit) {
        //是否拒绝不能为空!
        if(request == null || request.getIsRefuseClosed() == null) {
            BizException.throwEx(getText("ime.quality.eightdreport.isRefuseClosed.notnull"));
        }
        //D8
        if (request.getIsRefuseClosed()) {
            OSCFieldRequiredUtils.validateAndThrow(request, "D8_TRUE");
        } else {
            OSCFieldRequiredUtils.validateAndThrow(request, "D8_FALSE");
        }
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (null == report) {
            throwDataNotExist();
        }
        if (checkStatus) {
            checkStatus(report, OSCEightDReportEnum.D8ClosedEvaluate);
        }
        //检查操作人
        checkOperator(report, OSCEightDReportEnum.D8ClosedEvaluate);

        //是否拒绝结案
        if (bSubmit && request.getIsRefuseClosed()) {
            setStatus(report, OSCEightDReportEnum.D2ProblemDefine);
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D2ProblemDefine)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        } else if (bSubmit) {
            setStatus(report, OSCEightDReportEnum.Finish);
            //删除D7提交消息 todo
//            imeNotifySenderServiceClient.delToUserCode(Lists.newArrayList(getStepUserCode(report, EightDReportOperateEnum.D8ClosedEvaluate)),
//                    getTodoByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
            //发送消息 todo
//            imeNotifySenderServiceClient.sendToUserId(Lists.newArrayList(report.getCreateUserId()),
//                    getFinshByImproveObject(report.getImproveObject()), buildMsgParam(report), report.getGid().toString());
        }
        //赋值
        report.getEightDReportRc().setD8FileGroupGid(bindFileGroup(request.getD8FileGroupGid(), request.getD8FileInfoList()));
        report.getEightDReportRc().setIsRefuseClosed(request.getIsRefuseClosed());
        report.getEightDReportRc().setClosedEvaluate(request.getClosedEvaluate());
        report.getEightDReportRc().setRefuseReason(request.getRefuseReason());
        //修改时间
        report.setModifyTime(Calendar.getInstance());
        return dao.save(report);
    }

    @Override
    protected void beforeDelete(OSCEightDReport entity, String message, Map<String, Object> extraMap) {
        boolean checkStatus = checkStatus(entity, OSCEightDReportEnum.D0ProblemPropose);
        if(!checkStatus) {
            throwStatusNotAllow("D0问题提出");
        }
    }

    /**
     * 获取主单详情
     * @param request
     * @return
     */
    @Override
    public OSCEightDReport findIncludeRcById(EightDReportDispatchRequest request) {
        if(request == null || request.getReportId() == null) {
            throwParamIsBlank();
        }
        OSCEightDReport report = dao.findById(request.getReportId()).orElse(null);
        if (ObjectUtils.isEmpty(report)) {
            return null;
        }
        OSCEightDReport model = new OSCEightDReport();
        BeanUtil.copyProperties(report, model, "eightDReportRc", "eightDReportDetailList");
        //getEightDReportRc
        if (!ObjectUtils.isEmpty(report.getEightDReportRc())) {
            OSCEightDReportRc eightDReportRc = new OSCEightDReportRc();
            BeanUtil.copyProperties(report.getEightDReportRc(), eightDReportRc);
            model.setEightDReportRc(eightDReportRc);
        }
        return model;
    }

    @Override
    public void dispatchSave(EightDReportDispatchRequest request) {
        if(request == null || request.getReportId() == null) {
            throwParamIsBlank();
        }
        OSCEightDReport entity = dao.getById(request.getReportId());
        if(entity == null) {
            throwDataNotExist();
        }
        OSCEightDReportEdit eightDReportEdit = getEightDReportEdit(entity);
        //检验修改时间
//        checkModifyTime(entity, request.getModifyTime());
        //小组成立
        boolean needException = false;
        if (eightDReportEdit.getIsEditD1()) {
            groupSetupSave(request, true);
            //如果拒绝接受，直接返回
            if (!request.getIsAcceptProblem()) {
                return;
            }
        }

        //问题界定
        if (eightDReportEdit.getIsEditD2() && eightDReportEdit.getIsEditD1()) {
            problemDefineSave(request, false);
        } else if (eightDReportEdit.getIsEditD2()) {
            problemDefineSave(request, true);
        }

        //围堵策略
        if (eightDReportEdit.getIsEditD3() && eightDReportEdit.getIsEditD2()) {
            request.setD3DirectClosure(false);
            containmentMeasureSave(request, false);
        } else if (eightDReportEdit.getIsEditD3()) {
            containmentMeasureSave(request, true);
        }

        //原因分析
        if (eightDReportEdit.getIsEditD4() && eightDReportEdit.getIsEditD3()) {
            reasonAnalysisSave(request, false);
        } else if (eightDReportEdit.getIsEditD4()) {
            reasonAnalysisSave(request, true);
        }

        //纠正措施
        if (eightDReportEdit.getIsEditD5() && eightDReportEdit.getIsEditD4()) {
            correctMeasureSave(request, false);
        } else if (eightDReportEdit.getIsEditD5()) {
            correctMeasureSave(request, true);
        }

        //效果验证
        if (eightDReportEdit.getIsEditD6() && eightDReportEdit.getIsEditD5()) {
            request.setIsEffective(true);
            effectVerifySave(request, false);
        } else if (eightDReportEdit.getIsEditD6()) {
            effectVerifySave(request, true);
        }

        //预防再发生
        if (eightDReportEdit.getIsEditD7() && eightDReportEdit.getIsEditD6()) {
            preventMeasuresSave(request, false);
        } else if (eightDReportEdit.getIsEditD7()) {
            preventMeasuresSave(request, true);
        }
    }

    @Override
    public void dispatchSubmit(EightDReportDispatchRequest request) {
        if(request == null || request.getReportId() == null) {
            throwParamIsBlank();
        }
        OSCEightDReport entity = dao.getById(request.getReportId());
        if(entity == null) {
            throwDataNotExist();
        }
        OSCEightDReportEdit eightDReportEdit = getEightDReportEdit(entity);
        //检验修改时间
//        checkModifyTime(entity, request.getModifyTime());
        //小组成立
        boolean needException = false;
        if (eightDReportEdit.getIsEditD1()) {
            groupSetupSubmit(request, true);
            //如果拒绝接受，直接返回
            if (!request.getIsAcceptProblem()) {
                return;
            }
        }

        //问题界定
        if (eightDReportEdit.getIsEditD2() && eightDReportEdit.getIsEditD1()) {
            problemDefineSubmit(request, false);
        } else if (eightDReportEdit.getIsEditD2()) {
            problemDefineSubmit(request, true);
        }

        //围堵策略
        if (eightDReportEdit.getIsEditD3() && eightDReportEdit.getIsEditD2()) {
            request.setD3DirectClosure(false);
            containmentMeasureSubmit(request, false);
        } else if (eightDReportEdit.getIsEditD3()) {
            containmentMeasureSubmit(request, true);
        }

        //原因分析
        if (eightDReportEdit.getIsEditD4() && eightDReportEdit.getIsEditD3()) {
            reasonAnalysisSubmit(request, false);
        } else if (eightDReportEdit.getIsEditD4()) {
            reasonAnalysisSubmit(request, true);
        }

        //纠正措施
        if (eightDReportEdit.getIsEditD5() && eightDReportEdit.getIsEditD4()) {
            correctMeasureSubmit(request, false);
        } else if (eightDReportEdit.getIsEditD5()) {
            correctMeasureSubmit(request, true);
        }

        //效果验证
        if (eightDReportEdit.getIsEditD6() && eightDReportEdit.getIsEditD5()) {
            request.setIsEffective(true);
            effectVerifySubmit(request, false);
        } else if (eightDReportEdit.getIsEditD6()) {
            effectVerifySubmit(request, true);
        }

        //预防再发生
        if (eightDReportEdit.getIsEditD7() && eightDReportEdit.getIsEditD6()) {
            preventMeasuresSubmit(request, false);
        } else if (eightDReportEdit.getIsEditD7()) {
            preventMeasuresSubmit(request, true);
        }
    }

    @Override
    public void internalDispatchSave(EightDReportDispatchRequest request) {
        if(request == null || request.getReportId() == null) {
            throwParamIsBlank();
        }
        OSCEightDReport entity = dao.getById(request.getReportId());
        if(entity == null) {
            throwDataNotExist();
        }
        //检验修改时间
//        checkModifyTime(entity, request.getModifyTime());

        OSCEightDReportEdit eightDReportEdit = getEightDReportEdit(entity);

        boolean needException = false;
        //问题界定
        if (eightDReportEdit.getIsEditD2()) {
            problemDefineSave(request, true);
        }

        //围堵策略
        if (eightDReportEdit.getIsEditD3() && eightDReportEdit.getIsEditD2()) {
            containmentMeasureSave(request, false);
        } else if (eightDReportEdit.getIsEditD3()) {
            containmentMeasureSave(request, true);
        }

        //原因分析
        if (eightDReportEdit.getIsEditD4() && eightDReportEdit.getIsEditD3()) {
            reasonAnalysisSave(request, false);
        } else if (eightDReportEdit.getIsEditD4()) {
            reasonAnalysisSave(request, true);
        }

        //纠正措施
        if (eightDReportEdit.getIsEditD5() && eightDReportEdit.getIsEditD4()) {
            correctMeasureSave(request, false);
        } else if (eightDReportEdit.getIsEditD5()) {
            correctMeasureSave(request, true);
        }

        //效果验证
        if (eightDReportEdit.getIsEditD6() && eightDReportEdit.getIsEditD5()) {
            effectVerifySave(request, false);
        } else if (eightDReportEdit.getIsEditD6()) {
            effectVerifySave(request, true);
        }
        //预防再发生
        if (eightDReportEdit.getIsEditD7() && eightDReportEdit.getIsEditD6()) {
            preventMeasuresSave(request, true);
        } else if (eightDReportEdit.getIsEditD7()) {
            preventMeasuresSave(request, true);
        }
    }

    /**
     * 抛异常-数据不存在
     */
    public void throwDataNotExist() {
        BizException.throwEx(getText("common.valid.dataNotExist"));
    }

    @Override
    public void internalDispatchSubmit(EightDReportDispatchRequest request) {
        OSCEightDReport entity = dao.getById(request.getReportId());
        if(entity == null) {
            throwDataNotExist();
        }
        //检验修改时间
//        checkModifyTime(entity, request.getModifyTime());
        boolean needException = false;
        OSCEightDReportEdit eightDReportEdit = getEightDReportEdit(entity);
        //问题界定
        if (eightDReportEdit.getIsEditD2()) {
            problemDefineSubmit(request, true);
        }
        //围堵策略
        if (eightDReportEdit.getIsEditD3() && eightDReportEdit.getIsEditD2()) {
            containmentMeasureSubmit(request, true);
        } else if (eightDReportEdit.getIsEditD3()) {
            containmentMeasureSubmit(request, true);
        }
        //如果直接结案，则调用保存接口
        if (eightDReportEdit.getIsEditD3() && null != request.getD3DirectClosure() && request.getD3DirectClosure()) {
            //todo
            return;
        }

        //原因分析
        if (eightDReportEdit.getIsEditD4() && eightDReportEdit.getIsEditD3()) {
            reasonAnalysisSubmit(request, true);
        } else if (eightDReportEdit.getIsEditD4()) {
            reasonAnalysisSubmit(request, true);
        }

        //纠正措施
        if (eightDReportEdit.getIsEditD5() && eightDReportEdit.getIsEditD4()) {
            correctMeasureSubmit(request, true);
        } else if (eightDReportEdit.getIsEditD5()) {
            correctMeasureSubmit(request, true);
        }
        //效果验证
        if (eightDReportEdit.getIsEditD6() && eightDReportEdit.getIsEditD5()) {
            effectVerifySubmit(request, true);
        } else if (eightDReportEdit.getIsEditD6()) {
            effectVerifySubmit(request, true);
        }
        if (eightDReportEdit.getIsEditD5() && null != request.getIsEffective() && !request.getIsEffective()) {
            //预防再发生
            if (eightDReportEdit.getIsEditD7()) {
                preventMeasuresSave(request, false);
            }
            return;
        }

        //预防再发生
        if (eightDReportEdit.getIsEditD7() && eightDReportEdit.getIsEditD6()) {
            preventMeasuresSubmit(request, true);
        } else if (eightDReportEdit.getIsEditD7()) {
            preventMeasuresSubmit(request, true);
        }
    }

    /**
     * 根据改进对象获取消息模板编码
     *
     * @param improveObject
     * @return
     */
    public String getTodoByImproveObject(String improveObject) {
        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(improveObject))
            return EightDReportConstant.Notify.SUPPLIER_TODO;
        if (ImproveObjectEnum.INTERNAL_IMPROVE.name().equals(improveObject)) return EightDReportConstant.Notify.TODO;
        return EightDReportConstant.Notify.TODO;
    }

    /**
     * 根据改进对象获取消息模板编码
     *
     * @param improveObject
     * @return
     */
    public String getBackByImproveObject(String improveObject) {
        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(improveObject))
            return EightDReportConstant.Notify.SUPPLIER_BACK;
        if (ImproveObjectEnum.INTERNAL_IMPROVE.name().equals(improveObject)) return EightDReportConstant.Notify.BACK;
        return EightDReportConstant.Notify.BACK;
    }

    /**
     * 根据改进对象获取消息模板编码
     *
     * @param improveObject
     * @return
     */
    public String getFinshByImproveObject(String improveObject) {
        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(improveObject))
            return EightDReportConstant.Notify.SUPPLIER_FINSH;
        if (ImproveObjectEnum.INTERNAL_IMPROVE.name().equals(improveObject)) return EightDReportConstant.Notify.FINSH;
        return EightDReportConstant.Notify.FINSH;
    }

    /**
     * 校验操作人
     *
     * @param report
     * @param operateEnum
     * @return
     */
    public void checkOperator(OSCEightDReport report, OSCEightDReportEnum operateEnum) {
//        //如果是供应商改进
//        if (report.getImproveObject().equals(ImproveObjectEnum.SUPPLIER_IMPROVE.name())) {
//            if (EightDReportOperateEnum.D0ProblemPropose.equals(operateEnum)) {
//                if (!report.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
//                    throwHandlerNotAllow(operateEnum.name());
//                }
//            }
//            if (EightDReportOperateEnum.D8ClosedEvaluate.equals(operateEnum)) {
//                if (!report.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
//                    throwHandlerNotAllow(operateEnum.name());
//                }
//            }
//        }

        //内部改进
//        if (EightDReportOperateEnum.D0ProblemPropose.equals(operateEnum)) {
//            if(!report.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
//                throwHandlerNotAllow(operateEnum.name());
//            }
//        }
//        if (EightDReportOperateEnum.D1GroupSetup.equals(operateEnum)) {
//            if(!report.getDutyDeptUserCode().equals(CurrentContextHelper.getUserCode())) {
//                throwHandlerNotAllow(operateEnum.name());
//            }
//        }

        if (CollectionUtils.isEmpty(report.getEightDReportDetailList())) {
            throwHandlerNotAllow(operateEnum.name());
        }
        Map<String, OSCEightDReportDetail> mapStepUserCode = report.getEightDReportDetailList().stream().collect(Collectors.toMap(OSCEightDReportDetail::getProcessStep, a -> a, (k1, k2) -> k1));
        if(!mapStepUserCode.get(operateEnum.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            throwHandlerNotAllow(operateEnum.name());
        }
    }

    /**
     * 获得编辑权限
     *
     * @param eightDReport
     * @return
     */
    public OSCEightDReportEdit getEightDReportEdit(OSCEightDReport eightDReport) {
        //如果是供应商改进
//        if (eightDReport.getImproveObject().equals(ImproveObjectEnum.SUPPLIER_IMPROVE.name())) {
//            return getSupplierEdit(eightDReport);
//        } else {
            return getInternalEdit(eightDReport);
//        }
    }

    /**
     * 内部改进编辑权限
     *
     * @param eightDReport
     * @return
     */
    protected OSCEightDReportEdit getInternalEdit(OSCEightDReport eightDReport) {
        OSCEightDReportEdit eightDReportEdit = new OSCEightDReportEdit();
        eightDReportEdit.setIsEditD0(false);
        eightDReportEdit.setIsEditD1(false);
        eightDReportEdit.setIsEditD2(false);
        eightDReportEdit.setIsEditD3(false);
        eightDReportEdit.setIsEditD4(false);
        eightDReportEdit.setIsEditD5(false);
        eightDReportEdit.setIsEditD6(false);
        eightDReportEdit.setIsEditD7(false);
        eightDReportEdit.setIsEditD8(false);

        //如果是内部改进
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D0ProblemPropose.name())
                && eightDReport.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
            eightDReportEdit.setIsEditD0(true);
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D1GroupSetup.name())
                && eightDReport.getOwnerUserId().equals(CurrentContextHelper.getUserId())) {
            eightDReportEdit.setIsEditD1(true);
        }

        boolean needCheckStatus = true;
        if (CollectionUtils.isEmpty(eightDReport.getEightDReportDetailList())) {
            return eightDReportEdit;
        }
        Map<String, OSCEightDReportDetail> mapStepUserCode = eightDReport.getEightDReportDetailList().stream().collect(Collectors.toMap(OSCEightDReportDetail::getProcessStep, a -> a, (k1, k2) -> k1));
        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D2ProblemDefine.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D2ProblemDefine.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD2(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D3ContainmentMeasure.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D3ContainmentMeasure.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD3(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D4ReasonAnalysis.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D4ReasonAnalysis.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD4(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D5CorrectMeasure.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D5CorrectMeasure.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD5(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D6EffectVerify.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D6EffectVerify.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD6(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if ((!needCheckStatus || eightDReport.getBillStatus().equals(OSCEightDReportEnum.D7RreventMeasures.name()))
                && mapStepUserCode.get(OSCEightDReportEnum.D7RreventMeasures.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD7(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }

        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D8ClosedEvaluate.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D8ClosedEvaluate.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            eightDReportEdit.setIsEditD8(true);
            needCheckStatus = false;
        } else {
            needCheckStatus = true;
        }


        return eightDReportEdit;
    }


    /**
     * 供应商改进编辑权限
     *
     * @param eightDReport
     * @return
     */
    protected OSCEightDReportEdit getSupplierEdit(OSCEightDReport eightDReport) {
        OSCEightDReportEdit eightDReportEdit = new OSCEightDReportEdit();
        eightDReportEdit.setIsEditD0(false);
        eightDReportEdit.setIsEditD1(true);
        eightDReportEdit.setIsEditD2(true);
        eightDReportEdit.setIsEditD3(true);
        eightDReportEdit.setIsEditD4(true);
        eightDReportEdit.setIsEditD5(true);
        eightDReportEdit.setIsEditD6(true);
        eightDReportEdit.setIsEditD7(true);
        eightDReportEdit.setIsEditD8(false);

        //D0需要根据创建人判断是否有权限
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D0ProblemPropose.name())
                && eightDReport.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
            eightDReportEdit.setIsEditD0(true);
        }

        //D8需要根据创建人判断是否有权限
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D8ClosedEvaluate.name())
                && eightDReport.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
            eightDReportEdit.setIsEditD8(true);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D1GroupSetup.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD1(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D2ProblemDefine.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD2(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D3ContainmentMeasure.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD3(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D4ReasonAnalysis.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD4(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D5CorrectMeasure.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD5(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D6EffectVerify.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD6(false);
        }

        //其他的步骤只判断状态不判断用户
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D7RreventMeasures.name())) {
            return eightDReportEdit;
        } else {
            eightDReportEdit.setIsEditD7(false);
        }

        return eightDReportEdit;
    }

    /**
     * 是否有撤销权限
     *
     * @param eightDReport
     * @return
     */
    public boolean getEightDReportRevoke(OSCEightDReport eightDReport) {

        //D0状态不让撤回
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D0ProblemPropose.name())) {
            return false;
        }

        //D1状态看创建人
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D1GroupSetup.name())) {
            if (eightDReport.getCreateUserId().equals(CurrentContextHelper.getUserId())) {
                return true;
            } else {
                return false;
            }
        }

        //D2状态看责任部门
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D2ProblemDefine.name())
                && eightDReport.getOwnerUserId().equals(CurrentContextHelper.getUserId())) {
            return true;
        }

        if (CollectionUtils.isEmpty(eightDReport.getEightDReportDetailList())) return false;
        Map<String, OSCEightDReportDetail> mapStepUserCode = eightDReport.getEightDReportDetailList().stream().collect(Collectors.toMap(OSCEightDReportDetail::getProcessStep, a -> a, (k1, k2) -> k1));
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D3ContainmentMeasure.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D2ProblemDefine.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D4ReasonAnalysis.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D3ContainmentMeasure.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D5CorrectMeasure.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D4ReasonAnalysis.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D6EffectVerify.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D5CorrectMeasure.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D7RreventMeasures.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D6EffectVerify.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.D8ClosedEvaluate.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D7RreventMeasures.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        if (eightDReport.getBillStatus().equals(OSCEightDReportEnum.Finish.name())
                && mapStepUserCode.get(OSCEightDReportEnum.D8ClosedEvaluate.name()).getUserCode().equals(CurrentContextHelper.getUserCode())) {
            return true;
        }
        return false;
    }

    /**
     * 打印PDF
     * @param reportId
     * @param request
     * @param response
     */
    @Override
    public void printPdf(Long reportId, HttpServletRequest request, HttpServletResponse response) {
        OSCEightDReport eightDReport = dao.getById(reportId);
        if (null == eightDReport) {
            BizException.throwEx(getText("common.valid.dataNotExist"));
        }
        String file = generate(eightDReport);
        //下载文件
        OSCFileUtils.fileResponse(file, request, response);
    }

    public Map<String, OSCEightDReportEnum> getStatusEnum() {
        Map<String, OSCEightDReportEnum> map = Maps.newLinkedHashMap();
        map.put(OSCEightDReportEnum.D0ProblemPropose.name(), OSCEightDReportEnum.D0ProblemPropose);
        map.put(OSCEightDReportEnum.D1GroupSetup.name(), OSCEightDReportEnum.D1GroupSetup);
        map.put(OSCEightDReportEnum.D2ProblemDefine.name(), OSCEightDReportEnum.D2ProblemDefine);
        map.put(OSCEightDReportEnum.D3ContainmentMeasure.name(), OSCEightDReportEnum.D3ContainmentMeasure);
        map.put(OSCEightDReportEnum.D4ReasonAnalysis.name(), OSCEightDReportEnum.D4ReasonAnalysis);
        map.put(OSCEightDReportEnum.D5CorrectMeasure.name(), OSCEightDReportEnum.D5CorrectMeasure);
        map.put(OSCEightDReportEnum.D6EffectVerify.name(), OSCEightDReportEnum.D6EffectVerify);
        map.put(OSCEightDReportEnum.D7RreventMeasures.name(), OSCEightDReportEnum.D7RreventMeasures);
        map.put(OSCEightDReportEnum.D8ClosedEvaluate.name(), OSCEightDReportEnum.D8ClosedEvaluate);
        map.put(OSCEightDReportEnum.Finish.name(), OSCEightDReportEnum.Finish);
        return map;
    }

    /**
     * 校验
     *
     * @param report
     * @param eightDReportOperateEnum
     * @return
     */
    public boolean checkStatus(OSCEightDReport report, OSCEightDReportEnum eightDReportOperateEnum) {
        Map<String, OSCEightDReportEnum> map = getStatusEnum();
        OSCEightDReportEnum statustEnum = map.get(eightDReportOperateEnum.name());
        return report.getBillStatus().equals(statustEnum.name());
    }

    /**
     * 校验上一节点状态
     *
     * @param report
     * @param eightDReportOperateEnum
     * @return
     */
    public boolean checkPreStatus(OSCEightDReport report, OSCEightDReportEnum eightDReportOperateEnum) {
        Map<String, OSCEightDReportEnum> map = getStatusEnum();
        Map.Entry<String, OSCEightDReportEnum> preNode = getPreNode(map, eightDReportOperateEnum);
        return report.getBillStatus().equals(preNode.getValue().name()) ? true : false;
    }

    /**
     * 设置
     *
     * @param report
     * @param eightDReportOperateEnum
     * @return
     */
    public void setStatus(OSCEightDReport report, OSCEightDReportEnum eightDReportOperateEnum) {
        Map<String, OSCEightDReportEnum> map = getStatusEnum();
        report.setBillStatus(map.get(eightDReportOperateEnum.name()).name());
    }

    /**
     * 撤回
     *
     * @param report
     * @param eightDReportOperateEnum
     * @return
     */
    public void revokeStatus(OSCEightDReport report, OSCEightDReportEnum eightDReportOperateEnum) {
        Map<String, OSCEightDReportEnum> map = getStatusEnum();
        Map.Entry<String, OSCEightDReportEnum> preNode = getPreNode(map, eightDReportOperateEnum);
        report.setBillStatus(preNode.getValue().name());
    }


    /**
     * 得到前一个节点
     *
     * @param map
     * @param eightDReportOperateEnum
     * @return
     */
    public Map.Entry<String, OSCEightDReportEnum> getPreNode(Map<String, OSCEightDReportEnum> map,
                                                             OSCEightDReportEnum eightDReportOperateEnum) {
        Map.Entry<String, OSCEightDReportEnum> preNode = null;
        for (Map.Entry<String, OSCEightDReportEnum> entry : map.entrySet()) {
            if (entry.getKey().equals(eightDReportOperateEnum.name())) {
                //如果为空则代表第一个节点，则返回当前节点
                return preNode == null ? entry : preNode;
            }
            preNode = entry;
        }
        return preNode;
    }

    @Override
    public String generate(OSCEightDReport eightDReport) {
        try {
            return doGenerate(eightDReport);
        } catch (Exception e) {
            e.printStackTrace();
            BizException.throwEx("生成PDF文件失败：" + e.getMessage());
        }
        return null;
    }

    /**
     * 生成
     *
     * @param eightDReport
     */
    protected String doGenerate(OSCEightDReport eightDReport) throws IOException, DocumentException {

        //创建Document
        OSCPdfDocument imePdfDocument = OSCPdfDocUtils.createDocument();
        imePdfDocument.setGeneralInfo("8D报告", CurrentContextHelper.getUserName(), eightDReport.getTaskNo(), eightDReport.getTaskNo(), CurrentContextHelper.getUserName());
        String file = System.getProperty("user.dir") + "/" + eightDReport.getTaskNo() + ".pdf";
        imePdfDocument.setPdfFilePath(file);

        //生成代码
        //标题
        Paragraph titlelGaragraph = OSCPdfDocUtils.getTitileParagraph("纠正预防（8D）报告\n" +
                "8D Corrective and Preventive Action Report");
        imePdfDocument.addElement(titlelGaragraph);

        //二维码
        BarcodeQRCode qrcode = new BarcodeQRCode(eightDReport.getTaskNo().trim(), 1, 1, null);
        Image qrcodeImage = qrcode.getImage();
        qrcodeImage.setAbsolutePosition(450, 750);
        qrcodeImage.scalePercent(200);
        imePdfDocument.addElement(qrcodeImage);

        //编码
        Paragraph noGaragraph = OSCPdfDocUtils.getParagraph("No: " + eightDReport.getTaskNo());
        imePdfDocument.addElement(noGaragraph);

        //D0 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D0：问题提出 Introduce the Problem"));
        //判断改进对象，根据详情添加不同内容
//        if (ImproveObjectEnum.SUPPLIER_IMPROVE.name().equals(eightDReport.getImproveObject())){
//            addD0Context2(imePdfDocument, eightDReport);
//        } else {
            addD0Context(imePdfDocument, eightDReport);
//        }

        //D1 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D1：小组成立 Approach the team"));
        addD1Context(imePdfDocument, eightDReport);

        //D2 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D2：问题界定 Describe the Problem"));
        addD2Context(imePdfDocument, eightDReport);

        //D3 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D3：围堵措施 Verify Containment Actions"));
        addD3Context(imePdfDocument, eightDReport);

        //D4 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D4：原因分析 Verify Root Causes"));
        addD4Context(imePdfDocument, eightDReport);

        //D5 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D5：纠正措施 Verify Permanent Corrective Actions"));
        addD5Context(imePdfDocument, eightDReport);

        //D6 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D6：效果验证 Validate Permanent Corrective Actions"));
        addD6Context(imePdfDocument, eightDReport);

        //D7 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D7：预防再发生 Permanent Recurrence & Standardization"));
        addD7Context(imePdfDocument, eightDReport);

        //D8 标题 内容
        imePdfDocument.addElement(getParagraphTitile("D8：结案评价 Congratulate the Team"));
        addD8Context(imePdfDocument, eightDReport);

        //生成文档
        imePdfDocument.generate();

        return file;
    }

    /**
     * 增加D0的内容-内部改进
     */
    protected void addD0Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        //D0的内容
        PdfPTable table = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
//        table.addCell(PdfDocumentUtils.createCellNoBorder("采购组织编码  Purchasing organization No.:" + PdfDocumentUtils.getValue(eightDReport.getPurchasingOrgCode())));
//        table.addCell(PdfDocumentUtils.createCellNoBorder("采购组织名称  Purchasing organization Name:" + PdfDocumentUtils.getValue(eightDReport.getPurchasingOrgName())));
//        table.addCell(PdfDocumentUtils.createCellNoBorder("供应商编码  Supplier code:" + PdfDocumentUtils.getValue(eightDReport.getVendorCode())));
//        table.addCell(PdfDocumentUtils.createCellNoBorder("供应商名称  Supplier name:" + PdfDocumentUtils.getValue(eightDReport.getVendorName())));
//        String problemSourceDictGroup = eightDReport.getImproveObject().equals(ImproveObjectEnum.SUPPLIER_IMPROVE.name()) ? EightDReportConstant.DictGroupName.SupplierProblemsSources : EightDReportConstant.DictGroupName.EightDReportProblemSource;
        /*table.addCell(PdfDocumentUtils.createCellNoBorder("问题来源 Origin Type: " + getEnumName(EightDReportConstant.DictGroupName.EightDReportProblemSource, eightDReport.getInternalProblemOrigin())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("来源单号 Origin No.: " + PdfDocumentUtils.getValue(eightDReport.getSourceNo())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("缺陷等级 Defect level: " + PdfDocumentUtils.getValue(eightDReport.getDefectGrade())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("重复发生次数 Number of repetitions: " + eightDReport.getRepeatTimes()));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料编码 Part No.: " + PdfDocumentUtils.getValue(eightDReport.getMaterialCode())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料名称 Part Name: " + PdfDocumentUtils.getValue(eightDReport.getMaterialName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("型号规格 Model specification: " + PdfDocumentUtils.getValue(eightDReport.getMaterialDesc())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料组 Material group: " + PdfDocumentUtils.getValue(eightDReport.getMaterialGroupName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("客户编码 Customer code: " + PdfDocumentUtils.getValue(eightDReport.getCustomerCode())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("客户名称 Customer name: " + PdfDocumentUtils.getValue(eightDReport.getCustomerName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("责任部门名称 Duty Department name: " + PdfDocumentUtils.getValue(eightDReport.getDutyDepartmentName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("责任部门负责人 Duty Department User Name: " + PdfDocumentUtils.getValue(eightDReport.getDutyDeptUserName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("回复期限 Reply deadline: " + dateFormat(eightDReport.getReplyPeriod())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("批次号 Batch No.: " + getStr(eightDReport.getLotCode())));*/
//        table.addCell(PdfDocumentUtils.createCellNoBorder("工厂 Plant name: " + getStr(eightDReport.getPlantName())));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("" ));
        imePdfDocument.addElement(table);
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("问题描述 Problem Description:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getProblemDesc()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getD0FileGroupGid())));
        PdfPTable table1 = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
        table1.addCell(OSCPdfDocUtils.createCellNoBorder("创建人 Creator: " + eightDReport.getCreateUserName()));
        table1.addCell(OSCPdfDocUtils.createCellNoBorder("创建时间  Date: " + dateFormatTime(eightDReport.getCreateTime())));
        imePdfDocument.addElement(table1);
    }

    public String getStr(String value) {
        return com.qzing.ieep.util.StringUtils.isBlank(value)?"":value;
    }

    /**
     * 增加D0的内容-供应商改进
     */
    protected void addD0Context2(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        //D0的内容
        PdfPTable table = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
        /*table.addCell(PdfDocumentUtils.createCellNoBorder("采购组织编码 Purchasing organization No.: " + PdfDocumentUtils.getValue(eightDReport.getPurchasingOrgCode())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("采购组织名称 Purchasing organization Name: " + PdfDocumentUtils.getValue(eightDReport.getPurchasingOrgName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("供应商编码 Supplier code: " + PdfDocumentUtils.getValue(eightDReport.getVendorErpCode())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("供应商名称 Supplier name: " + PdfDocumentUtils.getValue(eightDReport.getVendorName())));
//        String problemSourceDictGroup = eightDReport.getImproveObject().equals(ImproveObjectEnum.SUPPLIER_IMPROVE.name()) ? EightDReportConstant.DictGroupName.SupplierProblemsSources : EightDReportConstant.DictGroupName.EightDReportProblemSource;
        table.addCell(PdfDocumentUtils.createCellNoBorder("问题来源 Origin Type: " + getEnumName(EightDReportConstant.DictGroupName.SupplierProblemsSources, eightDReport.getProblemOrigin())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("来源单号 Origin No.: " + PdfDocumentUtils.getValue(eightDReport.getSourceNo())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料编码 Part No.: " + PdfDocumentUtils.getValue(eightDReport.getMaterialCode())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料名称 Part Name: " + PdfDocumentUtils.getValue(eightDReport.getMaterialName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("型号规格 Model specification: " + PdfDocumentUtils.getValue(eightDReport.getMaterialDesc())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("物料组 Material group: " + PdfDocumentUtils.getValue(eightDReport.getMaterialGroupName())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("缺陷等级 Defect level: " + PdfDocumentUtils.getValue(eightDReport.getDefectGrade())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("重复发生次数 Number of repetitions: " + eightDReport.getRepeatTimes()));


        table.addCell(PdfDocumentUtils.createCellNoBorder("回复期限 Reply deadline: " + dateFormat(eightDReport.getReplyPeriod())));
        table.addCell(PdfDocumentUtils.createCellNoBorder("批次号 Batch No.: " + PdfDocumentUtils.getValue(eightDReport.getLotCode())));*/
        imePdfDocument.addElement(table);
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("问题描述 Problem Description:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getProblemDesc()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getD0FileGroupGid())));
        PdfPTable table1 = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
        table1.addCell(OSCPdfDocUtils.createCellNoBorder("创建人 Creator: " + eightDReport.getCreateUserName()));
        table1.addCell(OSCPdfDocUtils.createCellNoBorder("创建时间 Date: " + dateFormatTime(eightDReport.getCreateTime())));
        imePdfDocument.addElement(table1);
    }

    /**
     * 转换日期格式
     * @param date
     * @return
     */
    public String dateFormat(Calendar date) {
        if(date == null) {
            return "";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return dateFormat.format(date.getTime());
    }

    /**
     * 转换日期格式
     * @param date
     * @return
     */
    public String dateFormatTime(Calendar date) {
        if(date == null) {
            return "";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return dateFormat.format(date.getTime());
    }

    /**
     * 增加D1的内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD1Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        PdfPTable table = OSCPdfDocUtils.createTable(5, Element.ALIGN_LEFT);
        table.addCell(OSCPdfDocUtils.createCellNoBorder("姓名\n" + "Name"));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("小组角色\n" + "Team role"));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("负责的流程步骤\n" + "Responsible steps"));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("电话\n" + "Tel."));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("邮箱\n" + "Mail"));

        if (!CollectionUtils.isEmpty(eightDReport.getEightDReportDetailList())) {
            for (OSCEightDReportDetail eightDReportDetail : eightDReport.getEightDReportDetailList()) {
                table.addCell(OSCPdfDocUtils.createCellNoBorder(eightDReportDetail.getUserName()));
                table.addCell(OSCPdfDocUtils.createCellNoBorder(getEnumName(EightDReportConstant.DictGroupName.QaTeamRole, eightDReportDetail.getRole())));
                table.addCell(OSCPdfDocUtils.createCellNoBorder(getEnumName(EightDReportConstant.DictGroupName.ProcessStep, eightDReportDetail.getProcessStep())));
                table.addCell(OSCPdfDocUtils.createCellNoBorder(eightDReportDetail.getPhone()));
                table.addCell(OSCPdfDocUtils.createCellNoBorder(eightDReportDetail.getEmail()));
            }
        }
        imePdfDocument.addElement(table);

    }

    /**
     * D2步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD2Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("问题界定 Problem Describetion:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getProblemDefine()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD2FileGroupGid())));
    }

    /**
     * D3步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD3Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("围堵措施 Containment Actions："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getContainmentMeasure()));
        if (null == eightDReport.getEightDReportRc().getD3DirectClosure()) {
            imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("直接结案 Direct Closure: "));
        } else if (eightDReport.getEightDReportRc().getD3DirectClosure()) {
            imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("直接结案 Direct Closure: " + "是"));
            imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("直接结案理由 Direct Closure Reason："));
            imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getD3DirectClosureReason()));
        } else {
            imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("直接结案 Direct Closure: " + "否"));
        }
    }

    /**
     * D4步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD4Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("原因分析 Root Causes："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getReasonAnalysis()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD4FileGroupGid())));
    }

    /**
     * D5 步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD5Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("纠正措施 Permanent Corrective Actions："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getCorrectMeasure()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD5FileGroupGid())));
        PdfPTable table = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
        table.addCell(OSCPdfDocUtils.createCellNoBorder("执行人 Executor: " + OSCPdfDocUtils.getValue(eightDReport.getEightDReportRc().getExecutor())));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("执行时间 Date: " + dateFormat(eightDReport.getEightDReportRc().getExecuteTime())));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("纠正措施回复时间 Reply Date: " + dateFormat(eightDReport.getEightDReportRc().getCorrectMeasureDate())));
        table.addCell(OSCPdfDocUtils.createCellNoBorder(" "));
        imePdfDocument.addElement(table);
    }


    /**
     * D6 步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD6Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("效果验证 Validate Permanent Corrective Actions："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getEffectVerify()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD6FileGroupGid())));
        PdfPTable table = OSCPdfDocUtils.createTable(2, Element.ALIGN_LEFT);
        table.addCell(OSCPdfDocUtils.createCellNoBorder("验证人 Verifier: " + OSCPdfDocUtils.getValue(eightDReport.getEightDReportRc().getVerifier())));
        table.addCell(OSCPdfDocUtils.createCellNoBorder("验证时间 Date: " + dateFormat(eightDReport.getEightDReportRc().getVerifyTime())));
        if (null == eightDReport.getEightDReportRc().getIsEffective()) {
            table.addCell(OSCPdfDocUtils.createCellNoBorder("对策是否有效  Is the countermeasure effective: "));
        } else if (eightDReport.getEightDReportRc().getIsEffective()) {
            table.addCell(OSCPdfDocUtils.createCellNoBorder("对策是否有效  Is the countermeasure effective: " + "是"));
        } else {
            table.addCell(OSCPdfDocUtils.createCellNoBorder("对策是否有效  Is the countermeasure effective: " + "否"));
        }
        table.addCell(OSCPdfDocUtils.createCellNoBorder(" "));
        imePdfDocument.addElement(table);
    }

    /**
     * D7 步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD7Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("预防措施 Standardization："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getPreventMeasures()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD7FileGroupGid())));
    }

    /**
     * D8 步骤内容
     *
     * @param imePdfDocument
     * @param eightDReport
     * @throws IOException
     * @throws DocumentException
     */
    protected void addD8Context(OSCPdfDocument imePdfDocument, OSCEightDReport eightDReport) throws IOException, DocumentException {
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("结案评价 Congratulations："));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(eightDReport.getEightDReportRc().getClosedEvaluate()));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph("附件 Attachment:"));
        imePdfDocument.addElement(OSCPdfDocUtils.getParagraph(fileName(eightDReport.getEightDReportRc().getD8FileGroupGid())));
    }

    /**
     * 得到段落标题
     *
     * @return
     */
    protected PdfPTable getParagraphTitile(String titile) throws IOException, DocumentException {
        PdfPTable table = OSCPdfDocUtils.createTable(1, Element.ALIGN_LEFT);
        table.setSpacingBefore(2f); //设置段落上空白
        table.setSpacingAfter(2f); //设置段落下空白
        PdfPCell cell = OSCPdfDocUtils.createCellNoBorder(titile);
        cell.setBackgroundColor(BaseColor.LIGHT_GRAY);
        table.addCell(cell);
        return table;
    }

    /**
     * 文件名称
     *
     * @param fileGid
     * @return
     */
    protected String fileName(Long fileGid) {
        if (null == fileGid) {
            return "";
        }
        RestResponse response = fileInfoClient.getAll(fileGid);
        if (response.isSuccess() && response.getData() != null) {
            List<FileInfoResponse> fileInfoList = JSON.parseArray(response.getData().toString(), FileInfoResponse.class);
            return fileInfoList.get(0).getFileName();
        }
        return "";
    }

    /**
     * 得到枚举的名称
     *
     * @param code        枚举分组编码
     * @param currentCode 当前枚举值编码
     * @return
     */
    protected String getEnumName(String code, String currentCode) {
        if (com.qzing.ieep.util.StringUtils.isBlank(code) || com.qzing.ieep.util.StringUtils.isBlank(currentCode)) {
            return "";
        }
        List list = dictValueExtService.getValue(code, "");
        for (int index = 0; index < list.size(); index++) {
            HashMap<String, String> map = (HashMap<String, String>) list.get(index);
            if (currentCode.equals(map.get("code"))) {
                return map.get("val");
            }
        }
        return currentCode;
    }

}

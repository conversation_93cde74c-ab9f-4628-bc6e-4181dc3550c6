package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.ieep.data.common.DataPermission;
import com.qzing.ieep.datadriven.DataDrivenEntityListener;
import com.qzing.ieep.logging.listener.AuditListener;

import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.Table;

/**
 * 风险疑点记录明细实体类
 */
@Entity
@Table(name = "d_osc_risksuspicionrecorddtl")
@DataPermission(billTypeCode = OSCBillType.FXYD)
public class RiskSuspicionRecordDtl extends StdRiskSuspicionRecordDtl {

    /**
     * 序列化ID，用于对象序列化时的版本控制
     */
    private static final long serialVersionUID = 2526335682158437861L;
}

package com.qzing.bsp.osc.util;

import com.qzing.ieep.util.StringUtils;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class OSCFileUtils {

    public static final String getFileName(String file) {
        if (StringUtils.isBlank(file)) {
            return "";
        }
        String[] temp = file.replaceAll("\\\\", "/").split("/");
        String myFileName = "";
        if (temp.length > 1) {
            myFileName = temp[(temp.length - 1)];
        }
        return myFileName;
    }

    public static void fileResponse(String file, HttpServletRequest request, HttpServletResponse response) {
        ServletOutputStream os = null;
        try {
            if (StringUtils.isBlank(file)) {
                return;
            }

            InputStream fis = new BufferedInputStream(new FileInputStream(file));
            byte[] bytes = new byte[fis.available()];
            fis.read(bytes);
            fis.close();

            String fileName = getFileName(file);
            String userAgent = request.getHeader("user-agent");

            if ((userAgent != null) &&
                    ((userAgent.indexOf("Firefox") >= 0) ||
                            (userAgent.indexOf("Chrome") >= 0) ||
                            (userAgent.indexOf("Safari") >= 0))) {
                fileName = new String(fileName.getBytes(), "ISO8859-1");
            } else {
                fileName = URLEncoder.encode(fileName, "UTF8");
            }

            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream; charset=UTF-8");
            response.setHeader("Content-disposition", "attachment; filename=\"" + fileName + "\"");

            os = response.getOutputStream();
            response.setContentLength(bytes.length);
            os.write(bytes);
            os.flush();
        } catch (Exception e) {
            e.printStackTrace();
            return;
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }
}
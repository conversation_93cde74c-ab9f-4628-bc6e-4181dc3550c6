package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;

/**
 * 风险疑点记录明细实体类-基类
 */
@Data
@MappedSuperclass
public class StdRiskSuspicionRecordDtl extends OSCEntity<Long> {

    private static final long serialVersionUID = 2526335682158437861L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "riskSuspicionRecordDtlId")
    @FieldMetadata(name = "明细ID", skipHistoryComparison = true)
    protected Long riskSuspicionRecordDtlId;

    @FieldMetadata(name = "主单", skipHistoryComparison = true)
    @ManyToOne
    @JoinColumn(name = "riskSuspicionRecordId", referencedColumnName = "riskSuspicionRecordId")
    protected RiskSuspicionRecord riskSuspicionRecord;


    @Column(name = "rowNo")
    @FieldMetadata(name = "行号", resourceKey = "label.rowNo")
    protected Integer rowNo;

    @Column(name = "suspicionDesc")
    @FieldMetadata(name = "疑点描述", resourceKey = "label.suspicionDesc")
    protected String suspicionDesc;

    @Column(name = "isGenerateJudge")
    @FieldMetadata(name = "是否生成研判", resourceKey = "label.isGenerateJudge")
    protected Integer isGenerateJudge = 0;

    @Column(name = "taskStatus")
    @FieldMetadata(name = "任务执行状态, 空、待派单、待执行、已完成、关闭", resourceKey = "OSCRISK.label.taskStatus")
    protected RiskJudgeState taskStatus;
    /**
     * 导入数据id
     */
    @ApiModelProperty(value = "导入数据id")
    @Transient
    protected String temporaldataId;
}

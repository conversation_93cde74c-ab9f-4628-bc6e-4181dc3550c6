package com.qzing.bsp.osc.client.impl;

import com.qzing.bsp.osc.client.OSCRiskSuspicionRecordClient;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.entity.RiskSuspicionRecord;
import com.qzing.bsp.osc.entity.RiskSuspicionRecordDtl;
import com.qzing.bsp.osc.service.RiskSuspicionRecordService;
import com.qzing.ieep.coding.template.client.ClientImplTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.log.Log;
import com.qzing.ieep.log.Logs;
import com.qzing.ieep.logging.properties.AddOperateLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 风险疑点记录中台接口实现类
 *
 * <AUTHOR>
 */
@Api(tags = "风险疑点记录中台接口")
@RestController
@RequestMapping("/${bsp.service.osc.version}/oscrisksuspicionrecord")
@AddOperateLog(moduleCode = OSCBillType.FXYD, entityClass = RiskSuspicionRecord.class)
public class OSCRiskSuspicionRecordClientImpl extends ClientImplTemplate<RiskSuspicionRecord, Long> implements OSCRiskSuspicionRecordClient {

    private static final Log LOGGER = Logs.getLog(OSCRiskSuspicionRecordClientImpl.class);
    @Autowired
    protected RiskSuspicionRecordService riskSuspicionRecordService;

    /**
     * 风险疑点记录明细导入
     *
     * @param requests 风险疑点记录明细request对象
     * @return
     */
    @Override
    @ApiOperation(value = "风险疑点记录明细导入")
    @PostMapping("/importDetailData")
    @AddOperateLog(action = "importDetailData")
    public RestResponse importDetailData(@RequestBody List<RiskSuspicionRecordDtl> requests) {
        return riskSuspicionRecordService.importDetailData(requests);
    }
}

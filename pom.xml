<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.qzing.ieep2</groupId>
        <artifactId>ieep2-parent</artifactId>
        <version>3.2.1.casic-SNAPSHOT</version>
        <relativePath />
    </parent>
    <groupId>com.qzing.bsp</groupId>
    <artifactId>bsp-osc-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <scm>
        <url>http://10.10.20.35/microservice/osc/osc</url>
        <connection>scm:git:http://10.10.20.35/microservice/osc/osc.git</connection>
        <developerConnection>scm:git:http://10.10.20.35/microservice/osc/osc.git</developerConnection>
    </scm>

    <properties>
        <qzing.ieep2.version>3.2.1.casic-SNAPSHOT</qzing.ieep2.version>
        <qzing.ieep2.integration.version>3.2.1.casic-SNAPSHOT</qzing.ieep2.integration.version>
        <qzing.ui.version>2.3.19-SNAPSHOT</qzing.ui.version>
        <qzing.lms.version>2.1.0-SNAPSHOT</qzing.lms.version>
        <!--<base.service.version>4.1.0-SNAPSHOT</base.service.version>-->
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.qzing.ieep2</groupId>
            <artifactId>ieep2-admin-client</artifactId>
        </dependency>
        <!-- prometheus客户端 -->
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <!-- nacos注册中心客户端 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity</artifactId>
            <version>1.7</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-dependencies</artifactId>
                <version>${qzing.ieep2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.qzing.ui</groupId>
                <artifactId>designer-runtime-all</artifactId>
                <version>${qzing.ui.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.service</groupId>
                <artifactId>service-base</artifactId>
                <version>${base.service.version}</version>
            </dependency>
            <dependency>
                <groupId>org.modelmapper</groupId>
                <artifactId>modelmapper</artifactId>
                <version>${modelmapper.version}</version>
            </dependency>
            <!-- 主数据 
            <dependency>
                <groupId>com.qzing.md</groupId>
                <artifactId>md-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.md</groupId>
                <artifactId>md-org-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.md</groupId>
                <artifactId>md-mdm-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.md</groupId>
                <artifactId>md-mdm-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.qzing.md</groupId>
                <artifactId>md-org-service</artifactId>
                <version>${project.version}</version>
            </dependency>-->
            <dependency>
                <groupId>com.qzing.lms</groupId>
                <artifactId>lms-service-baseLine</artifactId>
                <version>${qzing.lms.version}</version>
                <classifier>std</classifier>
            </dependency>
            <dependency>
                <groupId>com.qzing.lms</groupId>
                <artifactId>lms-control-baseLine</artifactId>
                <version>${qzing.lms.version}</version>
                <classifier>std</classifier>
            </dependency>
            <dependency>
                <groupId>com.qzing.ieep2</groupId>
                <artifactId>ieep2-uaa-api</artifactId>
                <version>${qzing.ieep2.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/extends</source>
                                <source>src/main/api</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <executions>
                    <execution>
                        <id>jar</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>std</classifier>
                            <includes>
                                <include>**/controller/Std**</include>
                                <include>**/dao/**</include>
                                <include>**/entity/Std*</include>
                                <include>**/schedule/Std*</include>
                                <include>**/service/third/external/Std*</include>
                                <include>**/service/third/internal/Std*</include>
                                <include>**/service/Std*</include>
                            </includes>
                        </configuration>
                    </execution>
                    <execution>
                        <id>stdentity</id>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                        <configuration>
                            <classifier>stdentity</classifier>
                            <includes>
                                <include>**/entity/Std*</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.codehaus.mojo</groupId>
                    <artifactId>build-helper-maven-plugin</artifactId>
                    <version>1.4</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <modules>
        <module>bsp-osc-api</module>
        <module>bsp-osc-service</module>
        <module>bsp-osc-service-thin</module>
    </modules>

</project>


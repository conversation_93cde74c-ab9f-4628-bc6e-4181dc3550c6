package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.OSCEightDReport;
import com.qzing.bsp.osc.model.EightDReportDispatchRequest;
import com.qzing.bsp.osc.model.EightDReportUpdateRequest;
import com.qzing.bsp.osc.service.OSCEightDReportService;
import com.qzing.ieep.coding.template.controller.ControllerTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.jpa.support.JpaSearchRequest;
import com.qzing.ieep.iam.api.UserClient;
import com.qzing.ieep.logging.properties.AddOperateLog;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class StdOSCEightDReportController extends ControllerTemplate<OSCEightDReport, Long> {

    @Autowired
    protected HttpServletRequest request;
    @Autowired
    protected HttpServletResponse response;
    @Autowired
    protected UserClient userClient;
    @Autowired
    protected OSCEightDReportService service;


//    @AddOperateLog(action = "button.quality.create")
//    public Long save(EightDReportCreateRequest request) {
//        //保存
//        return service.save(request);
//    }

    @AddOperateLog(action = "ime.quality.eightdreport.problemproposesave.action")
    @ApiOperation(value = "问题提出编辑")
    @PostMapping("/problemproposesave")
    public RestResponse problemProposeSave(@RequestBody EightDReportUpdateRequest request) {
        service.problemProposeSave(request);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.problemproposesubmit.action")
    @ApiOperation(value = "问题提出提交")
    @PostMapping("/problemproposesubmit")
    public RestResponse problemProposeSubmit(@RequestBody EightDReportUpdateRequest request) {
        service.problemProposeSubmit(request);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.groupsetupsave.action")
    @ApiOperation(value = "问题提出提交")
    @PostMapping("/groupsetupsave")
    public RestResponse groupSetupSave(@RequestBody EightDReportDispatchRequest request) {
        //小组成立编辑
        service.groupSetupSave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.groupsetupsubmit.action")
    @ApiOperation(value = "小组成立提交")
    @PostMapping("/groupsetupsubmit")
    public RestResponse groupSetupSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.groupSetupSubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.problemdefinesave.action")
    @ApiOperation(value = "问题界定编辑")
    @PostMapping("/problemdefinesave")
    public RestResponse problemDefineSave(@RequestBody EightDReportDispatchRequest request) {
        service.problemDefineSave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.problemdefinesubmit.action")
    @ApiOperation(value = "问题界定提交")
    @PostMapping("/problemdefinesubmit")
    public RestResponse problemDefineSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.problemDefineSubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.containmentmeasuresave.action")
    @ApiOperation(value = "围堵措施编辑")
    @PostMapping("/containmentmeasuresave")
    public RestResponse containmentMeasureSave(@RequestBody EightDReportDispatchRequest request) {
        service.containmentMeasureSave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.containmentmeasuresubmit.action")
    @ApiOperation(value = "围堵措施提交")
    @PostMapping("/containmentmeasuresubmit")
    public RestResponse containmentMeasureSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.containmentMeasureSubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.reasonanalysissave.action")
    @ApiOperation(value = "原因分析编辑")
    @PostMapping("/reasonanalysissave")
    public RestResponse reasonAnalysisSave(@RequestBody EightDReportDispatchRequest request) {
        service.reasonAnalysisSave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.reasonanalysissubmit.action")
    @ApiOperation(value = "原因分析提交")
    @PostMapping("/reasonanalysissubmit")
    public RestResponse reasonAnalysisSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.reasonAnalysisSubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.correctmeasuresave.action")
    @ApiOperation(value = "纠正措施编辑")
    @PostMapping("/correctmeasuresave")
    public RestResponse correctMeasureSave(@RequestBody EightDReportDispatchRequest request) {
        service.correctMeasureSave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.correctmeasuresubmit.action")
    @ApiOperation(value = "纠正措施提交")
    @PostMapping("/correctmeasuresubmit")
    public RestResponse correctMeasureSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.correctMeasureSubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.effectverifysave.action")
    @ApiOperation(value = "效果验证编辑")
    @PostMapping("/effectverifysave")
    public RestResponse effectVerifySave(@RequestBody EightDReportDispatchRequest request) {
        service.effectVerifySave(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.effectverifysubmit.action")
    @ApiOperation(value = "效果验证提交")
    @PostMapping("/effectverifysubmit")
    public RestResponse effectVerifySubmit(@RequestBody EightDReportDispatchRequest request) {
        service.effectVerifySubmit(request, true);
        return RestResponse.success();
    }


    @AddOperateLog(action = "ime.quality.eightdreport.rreventmeasuressave.action")
    @ApiOperation(value = "预防再发生编辑")
    @PostMapping("/preventMeasuresSave")
    public RestResponse preventMeasuresSave(@RequestBody EightDReportDispatchRequest request) {
        service.preventMeasuresSave(request, true);
        return RestResponse.success();
    }

    @AddOperateLog(action = "ime.quality.eightdreport.rreventmeasuressubmit.action")
    @ApiOperation(value = "预防再发生提交")
    @PostMapping("/preventmeasuressubmit")
    public RestResponse preventMeasuresSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.preventMeasuresSubmit(request, true);
        return RestResponse.success();
    }

    @AddOperateLog(action = "ime.quality.eightdreport.closedevaluatesave.action")
    @ApiOperation(value = "结案评价编辑")
    @PostMapping("/closedevaluatesave")
    public RestResponse closedEvaluateSave(@RequestBody EightDReportDispatchRequest request) {
        service.closedEvaluateSave(request, true);
        return RestResponse.success();
    }

    @AddOperateLog(action = "ime.quality.eightdreport.closedevaluatesubmit.action")
    @ApiOperation(value = "结案评价提交")
    @PostMapping("/closedevaluatesubmit")
    public RestResponse closedEvaluateSubmit(@RequestBody EightDReportDispatchRequest request) {
        service.closedEvaluateSubmit(request);
        return RestResponse.success();
    }

    @ApiOperation(value = "结案评价提交")
    @PostMapping("/getbyid")
    public RestResponse getById(@RequestBody EightDReportDispatchRequest request) {
        return RestResponse.success(service.findIncludeRcById(request));
    }

    //供应商改进调度保存
    @AddOperateLog(action = "QM.button.dispatchSave")
    public RestResponse dispatchSave(EightDReportDispatchRequest request) {
        service.dispatchSave(request);
        return RestResponse.success();
    }


    //供应商改进调度提交
    @AddOperateLog(action = "QM.button.dispatchSubmit")
    public RestResponse dispatchSubmit(EightDReportDispatchRequest request) {
        service.dispatchSubmit(request);
        return RestResponse.success();
    }

    //8d改进调度保存
    @AddOperateLog(action = "QM.button.internalDispatchSave")
    public void internalDispatchSave(EightDReportDispatchRequest request) {
        service.internalDispatchSave(request);
    }

    //8d改进调度提交
    @AddOperateLog(action = "QM.button.internalDispatchSubmit")
    public void internalDispatchSubmit(EightDReportDispatchRequest request) {
        service.internalDispatchSubmit(request);
    }

    @ApiOperation(value = "打印PDF")
    @PostMapping("/printpdf")
    public Response printPdf(@RequestParam(name = "reportId") Long reportId) {
        request.getSession();
        service.printPdf(reportId, request, response);
        return null;
    }

    @ApiOperation(value = "用户列表")
    @PostMapping("/finduserwithpage")
    public RestResponse findUserWithPage(@RequestBody JpaSearchRequest request) {
        return userClient.findAllWithPage(request.getFilter(), request.getPage(), request.getPageSize(), request.getSorted());
    }

}

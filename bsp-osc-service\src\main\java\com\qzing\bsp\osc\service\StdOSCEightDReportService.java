package com.qzing.bsp.osc.service;


import com.qzing.bsp.osc.entity.OSCEightDReport;
import com.qzing.bsp.osc.entity.OSCTask;
import com.qzing.bsp.osc.model.EightDReportDispatchRequest;
import com.qzing.bsp.osc.model.OSCEightDReportEdit;
import com.qzing.bsp.osc.model.EightDReportUpdateRequest;
import com.qzing.ieep.coding.template.service.ServiceTemplate;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface StdOSCEightDReportService extends ServiceTemplate<OSCEightDReport, Long> {

    /**
     * 由任务单创建
     * @param task
     */
    void saveFromTask(OSCTask task);

    /**
     * 8D改进管理问题提出编辑
     * @param request
     */
    void problemProposeSave(EightDReportUpdateRequest request);

    /**
     * 8D改进管理问题提出提交
     * @param request
     */
    void problemProposeSubmit(EightDReportUpdateRequest request);

    /**
     * 8D改进管理小组成立编辑保存
     * @param request
     * @param checkStatus
     */
    void groupSetupSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理小组成立编辑提交
     * @param request
     * @param checkStatus
     */
    void groupSetupSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理问题界定编辑
     * @param request
     * @param checkStatus
     */
    void problemDefineSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理问题界定提交
     * @param request
     * @param checkStatus
     */
    void problemDefineSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理围堵措施编辑
     * @param request
     * @param checkStatus
     */
    void containmentMeasureSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理围堵措施提交
     * @param request
     * @param checkStatus
     */
    void containmentMeasureSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 原因分析保存
     * @param request
     * @param checkStatus
     */
    void reasonAnalysisSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 原因分析提交
     * @param request
     * @param checkStatus
     */
    void reasonAnalysisSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 纠正措施保存
     * @param request
     * @param checkStatus
     */
    void correctMeasureSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 纠正措施提交
     * @param request
     * @param checkStatus
     */
    void correctMeasureSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 效果验证保存
     * @param request
     * @param checkStatus
     */
    void effectVerifySave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 效果验证提交
     * @param request
     * @param checkStatus
     */
    void effectVerifySubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 预防再发生保存
     * @param request
     * @param checkStatus
     */
    void preventMeasuresSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 预防再发生提交
     * @param request
     * @param checkStatus
     */
    void preventMeasuresSubmit(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理结案评价编辑
     * @param request
     * @param checkStatus
     */
    void closedEvaluateSave(EightDReportDispatchRequest request, boolean checkStatus);

    /**
     * 8D改进管理结案评价提交
     * @param request
     */
    void closedEvaluateSubmit(EightDReportDispatchRequest request);

//    List<BaseBatchResponse> revoke(List<Long> gidList);

    /**
     * 获取主单详情
     * @param request
     * @return
     */
    OSCEightDReport findIncludeRcById(EightDReportDispatchRequest request);

    void dispatchSave(EightDReportDispatchRequest request);

    void dispatchSubmit(EightDReportDispatchRequest request);

    void internalDispatchSave(EightDReportDispatchRequest request);

    void internalDispatchSubmit(EightDReportDispatchRequest request);

    OSCEightDReportEdit getEightDReportEdit(OSCEightDReport eightDReport);

    boolean getEightDReportRevoke(OSCEightDReport eightDReport);

    /**
     * 打印PDF
     * @param reportId
     * @param request
     * @param response
     */
    void printPdf(Long reportId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 生成pdf
     *
     * @param eightDReport
     */
    String generate(OSCEightDReport eightDReport);
}

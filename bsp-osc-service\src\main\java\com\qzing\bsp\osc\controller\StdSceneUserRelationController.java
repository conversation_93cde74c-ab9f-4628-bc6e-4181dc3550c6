package com.qzing.bsp.osc.controller;

import com.qzing.bsp.osc.entity.SceneUserRelation;
import com.qzing.bsp.osc.service.SceneUserRelationService;
import com.qzing.ieep.coding.template.controller.SimpleControllerTemplate;
import com.qzing.ieep.data.common.RestResponse;
import com.qzing.ieep.data.util.vo.BaseRequest;
import com.qzing.ieep.license.annotation.Certificate;
import com.qzing.ieep.license.annotation.Certificate.RequiredType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Api(tags = "场景人员关系中间层接口")
//@Certificate(value = {"OSC"}, requiredType = RequiredType.ONE)
public class StdSceneUserRelationController extends SimpleControllerTemplate<SceneUserRelation, Long> {

    @Autowired
    protected SceneUserRelationService service;

    /**
     * 启用
     *
     * @param ids 竞价方向IDs
     *
     * @return
     */
    @ApiOperation(value = "启用")
    @PostMapping("/enable")
    public RestResponse enable(@RequestBody List<Long> ids) {
        service.enable(ids, getUserInfo());
        return RestResponse.success();
    }

    /**
     * 禁用
     *
     * @param ids 竞价方向IDs
     * @return
     */
    @ApiOperation(value = "禁用")
    @PostMapping("/disable")
    public RestResponse disable(@RequestBody List<Long> ids) {
        service.disable(ids, getUserInfo());
        return RestResponse.success();
    }
    @PostMapping({"/deletebyid"})
    public RestResponse<?> deleteById(@RequestBody BaseRequest<SceneUserRelation,Long> request) {
        this.service.deleteData(request.getIds());
        return RestResponse.success();
    }
}

package com.qzing.bsp.osc.entity;

import com.qzing.bsp.osc.annotation.OSCFieldRequired;
import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@NoArgsConstructor
@Getter
@Setter
public class StdOSCEightDReportDetail extends OSCEntity<Long>  {

    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "detailId")
    @FieldMetadata(name = "ID", resourceKey = "label.reportId", skipHistoryComparison = true)
    protected Long detailId;

    @OSCFieldRequired()
    @FieldMetadata(name = "账号", resourceKey = "label.quality.accountNumber")
    protected String userCode;

    @OSCFieldRequired()
    @FieldMetadata(name = "姓名", resourceKey = "label.quality.user.name")
    protected String userName;

    @OSCFieldRequired()
    @FieldMetadata(name = "小组角色", resourceKey = "label.quality.teamRole")
    protected String role;

    @FieldMetadata(name = "流程步骤", resourceKey = "label.quality.processSteps")
    protected String processStep;

    @FieldMetadata(name = "电话", resourceKey = "label.quality.phone")
    protected String phone;

    @FieldMetadata(name = "邮箱", resourceKey = "label.quality.mailbox")
    protected String email;

    @FieldMetadata(name = "备注", resourceKey = "label.quality.note")
    protected String remark;

    @ManyToOne
    @JoinColumn(name = "reportId", referencedColumnName = "reportId")
    protected OSCEightDReport eightDReport;

}

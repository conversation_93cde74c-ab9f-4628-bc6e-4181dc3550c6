package com.qzing.bsp.osc.entity;

import com.qzing.ieep.data.common.FieldMetadata;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.MappedSuperclass;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Calendar;

@MappedSuperclass
@NoArgsConstructor
@Getter
@Setter
public class StdOSCEightDReportRecord extends OSCEntity<Long>  {

    private static final long serialVersionUID = 5180125584416159587L;

    @Id
    @GenericGenerator(strategy = "com.qzing.ieep.data.jpa.support.SnowFlakeIdGenerator", name = "SnowFlakeIdGenerator")
    @GeneratedValue(generator = "SnowFlakeIdGenerator")
    @Column(name = "recordId")
    @FieldMetadata(name = "ID", resourceKey = "label.reportId", skipHistoryComparison = true)
    protected Long recordId;

    @FieldMetadata(name = "8D改进管理gid")
    protected Long reportId;

    @FieldMetadata(name = "单据状态")
    protected String billStatus;

    @FieldMetadata(name = "操作人")
    protected String operator;

    @FieldMetadata(name = "操作日期")
    @Temporal(TemporalType.TIMESTAMP)
    protected Calendar operateTime;

    @FieldMetadata(name = "操作顺序号")
    protected Integer operateNum;

}

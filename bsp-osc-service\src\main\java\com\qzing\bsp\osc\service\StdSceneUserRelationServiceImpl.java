package com.qzing.bsp.osc.service;

import cn.hutool.core.collection.CollUtil;
import com.qzing.bsp.osc.annotation.ModuleInfo;
import com.qzing.bsp.osc.constant.OSCBillType;
import com.qzing.bsp.osc.entity.SceneUserRelation;
import com.qzing.bsp.osc.util.OSCFieldRequiredUtils;
import com.qzing.ieep.coding.template.service.SimpleServiceImplTemplate;
import com.qzing.ieep.core.exception.BizException;
import com.qzing.ieep.data.jpa.util.DataUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.context.i18n.LocaleContextHolder.getLocale;

/**
 * 场景人员关系服务实现类
 */
@ModuleInfo(moduleCode = OSCBillType.CJRY)
public class StdSceneUserRelationServiceImpl extends SimpleServiceImplTemplate<SceneUserRelation, Long> implements StdSceneUserRelationService {
    public void enable(List<Long> ids, Map<String, Object> extraInfo) {
        setEnableOrDis(ids, "enable");
    }

    @Override
    protected void beforeSave(SceneUserRelation entity, Map<String, Object> extraMap) {
        entity.setStatus(1);
    }

    public void disable(List<Long> ids, Map<String, Object> extraInfo) {
        setEnableOrDis(ids, "disable");
    }

    protected void setEnableOrDis(List<Long> ids, String action) {
        boolean isEnable = "enable".equals(action);
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("IN_sceneUserRelationId", ids);
        List<SceneUserRelation> sceneUserRelations = findAll(searchMap);
        if (null == sceneUserRelations || 0 == sceneUserRelations.size()) {
            BizException.throwEx(getText("bid.valid.noFound"));
        }

        for (SceneUserRelation paramGroup : sceneUserRelations) {
            paramGroup.setStatus(isEnable ? 1 : 0);
        }
        saveAll(sceneUserRelations);
    }

    @Override
    public void validate(SceneUserRelation entity, Map<String, Object> extraMap) {
        OSCFieldRequiredUtils.validFieldValue(DataUtils.parseMap(entity), new String[]{"sceneCode", "sceneName", "userCode", "userName"}, getLocale());
        //判断场景编码+用户编码是否已存在
        Map<String, Object> searchMap = new HashMap<>();
        searchMap.put("EQ_sceneCode", entity.getSceneCode());
        searchMap.put("EQ_userCode", entity.getUserCode());
        boolean isAdd = entity.getSceneUserRelationId() == null ? true : false;
        if (!isAdd) {
            searchMap.put("NE_sceneUserRelationId", entity.getSceneUserRelationId());
        }
        if (exists(searchMap)) {
            BizException.throwEx(getText("bid.valid.exists"));
        }
    }

    @Override
    public void deleteData(List<Long> ids) {
        if (CollUtil.isNotEmpty(ids)) {
            Map<String, Object> searchMap = new HashMap<>();
            searchMap.put("IN_sceneUserRelationId", ids);
            List<SceneUserRelation> userRelations = findAll(searchMap);
            if (CollUtil.isNotEmpty(userRelations)) {
                for (SceneUserRelation userRelation : userRelations) {
                    //存在启用的无法删除
                    if (Integer.valueOf(1).equals(userRelation.getStatus())) {
                        BizException.throwEx(getText("message.notOperationAndRefresh"));
                    }
                }
                for (SceneUserRelation userRelation : userRelations) {
                    addLog(userRelation, "delete");
                }
                deleteAll(userRelations);
            }
        }
    }
}

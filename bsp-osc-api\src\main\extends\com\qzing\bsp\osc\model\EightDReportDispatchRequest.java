package com.qzing.bsp.osc.model;

import com.qzing.bsp.osc.annotation.OSCFieldRequired;
import com.qzing.bsp.osc.entity.OSCEightDReportDetail;
import com.qzing.ieep.data.util.vo.FileInfoResponse;
import com.qzing.ieep.util.StringUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.Calendar;
import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@ApiModel("8D调度编辑提交参数")
public class EightDReportDispatchRequest implements Serializable {
    private static final long serialVersionUID = 5180125584416159587L;

    @OSCFieldRequired(area = {"preventMeasures", "effectVerify", "correctMeasure", "reasonAnalysis", "D3_TRUE", "D3_FALSE", "D8_TRUE", "D8_FALSE", "groupSetup"})
    @ApiModelProperty(value = "ID")
    protected Long reportId;

    @OSCFieldRequired(area = {"groupSetup"})
    @ApiModelProperty(value = "是否接受问题")
    protected Boolean isAcceptProblem;

    @OSCFieldRequired(area = {"groupSetup"})
    @ApiModelProperty(value = "接受拒绝理由")
    protected String acceptRefuseReason;

    @ApiModelProperty(value = "小组成员集合")
    protected List<OSCEightDReportDetail> groupMembersRequestList;

    @ApiModelProperty(value = "问题界定")
    protected String problemDefine;

    @ApiModelProperty(value = "D2 附件(8D)")
    protected Long d2FileGroupGid;

    @ApiModelProperty("D2 附件(8D)附件信息集合")
    protected List<FileInfoResponse> d2FileInfoList;

    @OSCFieldRequired(area = {"D3_FALSE"})
    @ApiModelProperty(value = "围堵措施")
    protected String containmentMeasure;

    @OSCFieldRequired(area = {"D3_TRUE", "D3_FALSE"})
    @ApiModelProperty(value = "D3 直接结案")
    protected Boolean d3DirectClosure;

    @OSCFieldRequired(area = {"D3_TRUE"})
    @ApiModelProperty(value = "D3 直接结案理由")
    protected String d3DirectClosureReason;

    @OSCFieldRequired(area = {"reasonAnalysis"})
    @ApiModelProperty(value = "原因分析")
    protected String reasonAnalysis;

    @ApiModelProperty(value = "D4附件（8D）")
    protected Long d4FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d4FileInfoList;

    @OSCFieldRequired(area = {"correctMeasure"})
    @ApiModelProperty(value = "纠正措施")
    protected String correctMeasure;

    @OSCFieldRequired(area = {"correctMeasure"})
    @ApiModelProperty(value = "执行人")
    protected String executor;

    @OSCFieldRequired(area = {"correctMeasure"})
    @ApiModelProperty(value = "执行日期")
    protected Calendar executeTime;

    @ApiModelProperty(value = "D5附件(8D)")
    protected Long d5FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d5FileInfoList;

    @OSCFieldRequired(area = {"effectVerify"})
    @ApiModelProperty(value = "效果验证")
    protected String effectVerify;

    @OSCFieldRequired(area = {"effectVerify"})
    @ApiModelProperty(value = "是否有效")
    protected Boolean isEffective;

    @OSCFieldRequired(area = {"effectVerify"})
    @ApiModelProperty(value = "验证人")
    protected String verifier;

    @OSCFieldRequired(area = {"effectVerify"})
    @ApiModelProperty(value = "验证时间")
    protected Calendar verifyTime;

    @ApiModelProperty(value = "D6附件（8D）")
    protected Long d6FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d6FileInfoList;

    @OSCFieldRequired(area = {"preventMeasures"})
    @ApiModelProperty(value = "预防措施")
    protected String preventMeasures;

    @ApiModelProperty(value = "D7附件(8D)")
    protected Long d7FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d7FileInfoList;

    @OSCFieldRequired(area = {"D8_TRUE", "D8_FALSE"})
    @ApiModelProperty(value = "是否拒绝")
    protected Boolean isRefuseClosed;

    @OSCFieldRequired(area = {"D8_TRUE"})
    @ApiModelProperty(value = "拒绝理由")
    protected String refuseReason;

    @OSCFieldRequired(area = {"D8_FALSE"})
    @ApiModelProperty(value = "结案评价")
    protected String closedEvaluate;

    @ApiModelProperty(value = "D8附件(8D)")
    protected Long d8FileGroupGid;

    @ApiModelProperty("附件信息集合")
    protected List<FileInfoResponse> d8FileInfoList;

    public Boolean checkD1Param() {
        return ObjectUtils.isEmpty(this.getIsAcceptProblem()) && StringUtils.isEmpty(this.getAcceptRefuseReason());
    }

    public Boolean checkD2Param() {
        return StringUtils.isEmpty(this.getProblemDefine()) && ObjectUtils.isEmpty(this.getD2FileGroupGid());
    }

    public Boolean checkD3Param() {
        return ObjectUtils.isEmpty(this.getD3DirectClosure()) && StringUtils.isEmpty(this.getD3DirectClosureReason());
    }

    public Boolean checkD4Param() {
        return StringUtils.isEmpty(this.getReasonAnalysis()) && ObjectUtils.isEmpty(this.getD4FileGroupGid());
    }

    public Boolean checkD5Param() {
        return StringUtils.isEmpty(this.getCorrectMeasure()) && StringUtils.isEmpty(this.getExecutor()) && ObjectUtils.isEmpty(this.getExecuteTime()) && ObjectUtils.isEmpty(this.getD5FileGroupGid());
    }

    public Boolean checkD6Param() {
        return StringUtils.isEmpty(this.getEffectVerify()) && ObjectUtils.isEmpty(this.getIsEffective()) && StringUtils.isEmpty(this.getVerifier()) && ObjectUtils.isEmpty(this.getVerifyTime()) && ObjectUtils.isEmpty(this.getD6FileGroupGid());
    }

    public Boolean checkD7Param() {
        return StringUtils.isEmpty(this.getPreventMeasures()) && ObjectUtils.isEmpty(this.getD7FileGroupGid());
    }

    public Boolean checkD8Param() {
        return ObjectUtils.isEmpty(this.getIsRefuseClosed()) && StringUtils.isEmpty(this.getRefuseReason()) && StringUtils.isEmpty(this.getClosedEvaluate()) && ObjectUtils.isEmpty(this.getD8FileGroupGid());
    }

}
